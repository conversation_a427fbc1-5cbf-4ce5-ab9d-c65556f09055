import React, { useState, useEffect } from 'react';
import { getComponentTypes } from '../services/iddService';

/**
 * Component to display a list of IDD components
 * @param {Object} props - Component props
 * @param {Function} props.onComponentSelect - Function to call when a component is selected
 * @param {Function} props.onDragStart - Function to call when a component is dragged
 * @returns {JSX.Element} - Rendered component
 */
const IDDComponentList = ({ onComponentSelect, onDragStart }) => {
  const [components, setComponents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filter, setFilter] = useState('');
  const [selectedGroup, setSelectedGroup] = useState('All');

  // Fetch component types on mount
  useEffect(() => {
    const fetchComponents = async () => {
      try {
        setLoading(true);
        const componentList = await getComponentTypes();
        setComponents(componentList);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching component types:', err);
        setError('Failed to load components. Please try again later.');
        setLoading(false);
      }
    };

    fetchComponents();
  }, []);

  // Get unique groups
  const groups = ['All', ...new Set(components.map(comp => comp.group))].filter(Boolean);

  // Filter components by group and search term
  const filteredComponents = components.filter(comp => {
    const matchesGroup = selectedGroup === 'All' || comp.group === selectedGroup;
    const matchesFilter = comp.name.toLowerCase().includes(filter.toLowerCase());
    return matchesGroup && matchesFilter;
  });

  // Group components by group
  const groupedComponents = filteredComponents.reduce((acc, comp) => {
    const group = comp.group || 'Other';
    if (!acc[group]) {
      acc[group] = [];
    }
    acc[group].push(comp);
    return acc;
  }, {});

  // Handle component selection
  const handleComponentSelect = (component) => {
    if (onComponentSelect) {
      // Use the internal type if available, otherwise use the original name
      onComponentSelect(component.internalType || component.name);
    }
  };

  // Handle component drag
  const handleDragStart = (event, component) => {
    if (onDragStart) {
      // Use the internal type if available, otherwise use the original name
      const componentType = component.internalType || component.name;
      onDragStart(event, componentType);
    }
  };

  if (loading) {
    return <div className="idd-component-list-loading">Loading components...</div>;
  }

  if (error) {
    return <div className="idd-component-list-error">{error}</div>;
  }

  return (
    <div className="idd-component-list">
      <div className="idd-component-list-header">
        <input
          type="text"
          placeholder="Search components..."
          value={filter}
          onChange={(e) => setFilter(e.target.value)}
          className="idd-component-list-search"
        />
        <select
          value={selectedGroup}
          onChange={(e) => setSelectedGroup(e.target.value)}
          className="idd-component-list-group-select"
        >
          {groups.map(group => (
            <option key={group} value={group}>{group}</option>
          ))}
        </select>
      </div>

      <div className="idd-component-list-content">
        {Object.entries(groupedComponents).map(([group, comps]) => (
          <div key={group} className="idd-component-list-group">
            <h3 className="idd-component-list-group-title">{group}</h3>
            <ul className="idd-component-list-items">
              {comps.map(comp => (
                <li
                  key={comp.name}
                  className="idd-component-list-item"
                  onClick={() => handleComponentSelect(comp)}
                  draggable
                  onDragStart={(event) => handleDragStart(event, comp)}
                >
                  {comp.name}
                  {comp.internalType && comp.internalType !== comp.name && (
                    <span className="idd-component-list-item-internal-type">
                      ({comp.internalType})
                    </span>
                  )}
                </li>
              ))}
            </ul>
          </div>
        ))}

        {Object.keys(groupedComponents).length === 0 && (
          <div className="idd-component-list-empty">
            No components found matching your criteria.
          </div>
        )}
      </div>
    </div>
  );
};

export default IDDComponentList;
