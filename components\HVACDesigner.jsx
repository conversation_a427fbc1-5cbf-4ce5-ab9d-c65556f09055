import React, { useState, useRef, useCallback } from 'react';
import React<PERSON>low, {
  ReactFlowProvider,
  Controls,
  Background,
  MiniMap,
  Panel
} from 'reactflow';
import { useHVACStore } from '../store/hvacStore';
import ComponentPanel from './ComponentPanel';
import PropertiesPanel from './PropertiesPanel';
import ToolbarPanel from './ToolbarPanel';
import { nodeTypes } from './nodes';
import { edgeTypes } from './edges';

const HVACDesigner = () => {
  const reactFlowWrapper = useRef(null);
  const [reactFlowInstance, setReactFlowInstance] = useState(null);

  // Get state from Zustand store
  const {
    nodes,
    edges,
    onNodesChange,
    onEdgesChange,
    onConnect,
    onNodeSelect,
    selectedNode,
    addNode
  } = useHVACStore();

  // Handle dropping components onto the canvas
  const onDragOver = useCallback((event) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  const onDrop = useCallback(
    (event) => {
      event.preventDefault();

      const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();
      const type = event.dataTransfer.getData('application/reactflow');
      const side = event.dataTransfer.getData('application/reactflow/side');

      // Check if the dropped element is valid
      if (typeof type === 'undefined' || !type) {
        return;
      }

      const position = reactFlowInstance.project({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      });

      // Check if we're dropping onto a parent node
      // If we are, the parent node's onDrop handler will handle it
      const nodeAtPosition = nodes.find(node => {
        if (node.type === 'airLoopHVAC' || node.type === 'plantLoop') {
          const { position: nodePos, width = 600, height = 400 } = node;
          return (
            position.x >= nodePos.x &&
            position.x <= nodePos.x + width &&
            position.y >= nodePos.y &&
            position.y <= nodePos.y + height
          );
        }
        return false;
      });

      // If we're not dropping onto a parent node, add the node to the canvas
      if (!nodeAtPosition) {
        // Add the new node to the store
        addNode(type, position);
      }
    },
    [reactFlowInstance, addNode, nodes]
  );

  return (
    <div className="hvac-designer">
      <div className="flow-container" ref={reactFlowWrapper}>
        <ReactFlowProvider>
          <ComponentPanel />
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            onInit={setReactFlowInstance}
            onDrop={onDrop}
            onDragOver={onDragOver}
            onNodeClick={(_, node) => onNodeSelect(node)}
            nodeTypes={nodeTypes}
            edgeTypes={edgeTypes}
            fitView
            deleteKeyCode="Delete"
            multiSelectionKeyCode="Control"
            snapToGrid={true}
            snapGrid={[15, 15]}
            // Enable nested nodes
            elementsSelectable={true}
            selectNodesOnDrag={false}
          >
            <Background color="#aaa" gap={16} />
            <Controls />
            <MiniMap
              nodeStrokeColor={(n) => {
                if (n.type === 'airHandler') return '#64b5f6';
                if (n.type === 'chiller') return '#4fc3f7';
                if (n.type === 'boiler') return '#ff8a65';
                if (n.type === 'coolingTower') return '#81c784';
                if (n.type === 'pump') return '#9575cd';
                if (n.type === 'vav') return '#7986cb';
                if (n.type === 'zone') return '#a1887f';
                return '#eee';
              }}
              nodeColor={(n) => {
                if (n.type === 'airHandler') return '#e3f2fd';
                if (n.type === 'chiller') return '#e1f5fe';
                if (n.type === 'boiler') return '#fff3e0';
                if (n.type === 'coolingTower') return '#e8f5e9';
                if (n.type === 'pump') return '#ede7f6';
                if (n.type === 'vav') return '#e8eaf6';
                if (n.type === 'zone') return '#efebe9';
                return '#fff';
              }}
              nodeBorderRadius={2}
            />
            <Panel position="top">
              <ToolbarPanel />
            </Panel>
          </ReactFlow>
        </ReactFlowProvider>
      </div>

      <PropertiesPanel node={selectedNode} />
    </div>
  );
};

export default HVACDesigner;
