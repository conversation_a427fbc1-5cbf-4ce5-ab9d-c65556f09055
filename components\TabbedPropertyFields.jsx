import React, { useState, useEffect } from 'react';
import iddService from '../services/iddService';
import { Tabs, Tab } from 'react-bootstrap';
import 'bootstrap/dist/css/bootstrap.min.css';

/**
 * Component to display property fields from the IDD file with tabs for referenced components
 */
const TabbedPropertyFields = ({ componentType, properties, onPropertyChange, onFieldsLoaded }) => {
  // State for the main component
  const [iddFields, setIddFields] = useState(null);
  const [loading, setLoading] = useState(true);

  // State for tabs
  const [activeTab, setActiveTab] = useState('main');
  const [tabs, setTabs] = useState([]);

  // State for referenced components - key is the field name (e.g., 'supplyFan')
  const [referencedComponents, setReferencedComponents] = useState({});

  // State to track selected values for each field
  const [selectedValues, setSelectedValues] = useState({});

  // State for nested field groups
  const [expandedGroups, setExpandedGroups] = useState({});

  // State for breadcrumb navigation
  const [breadcrumbs, setBreadcrumbs] = useState([{ id: 'main', title: componentType }]);

  // Fetch main component fields
  useEffect(() => {
    const fetchFields = async () => {
      try {
        setLoading(true);

        // Reset state when component type changes
        setActiveTab('main');
        setTabs([{ id: 'main', title: componentType }]);
        setReferencedComponents({});
        setSelectedValues({});
        setExpandedGroups({});
        setBreadcrumbs([{ id: 'main', title: componentType }]);

        const component = await iddService.getComponentProperties(componentType);

        if (component && component.fields) {
          setIddFields(component.fields);
          if (onFieldsLoaded) {
            onFieldsLoaded(component.fields);
          }
        } else {
          console.warn(`No fields found for ${componentType}`);
          if (onFieldsLoaded) {
            onFieldsLoaded(null);
          }
        }

        setLoading(false);
      } catch (error) {
        console.error(`Error fetching fields for ${componentType}:`, error);
        setLoading(false);
        if (onFieldsLoaded) {
          onFieldsLoaded(null);
        }
      }
    };

    if (componentType) {
      fetchFields();
    }
  }, [componentType, onFieldsLoaded]);

  // Toggle a field group's expanded state
  const toggleFieldGroup = (groupId) => {
    setExpandedGroups(prev => ({
      ...prev,
      [groupId]: !prev[groupId]
    }));
  };

  // Navigate to a specific tab via breadcrumb
  const navigateToBreadcrumb = (breadcrumbId) => {
    // Find the index of the clicked breadcrumb
    const index = breadcrumbs.findIndex(crumb => crumb.id === breadcrumbId);

    if (index >= 0) {
      // Truncate breadcrumbs to this point
      setBreadcrumbs(breadcrumbs.slice(0, index + 1));

      // Set the active tab
      setActiveTab(breadcrumbId);
    }
  };

  // Handle selecting a referenced component
  const handleSelectReference = async (selectedValue, fieldName, parentPath = []) => {
    try {
      console.log(`Selected ${selectedValue} for field ${fieldName}`);

      // Convert fieldName to camelCase for consistency
      const camelCaseFieldName = fieldName.charAt(0).toLowerCase() + fieldName.slice(1);

      // Create a unique tab ID that includes the parent path
      const pathString = parentPath.length > 0 ? `${parentPath.join('.')}.` : '';
      const tabId = `${pathString}${camelCaseFieldName}`;

      // Update the selected value for this field
      setSelectedValues(prev => ({
        ...prev,
        [tabId]: selectedValue
      }));

      // Fetch the referenced component
      const component = await iddService.getComponentProperties(selectedValue);

      if (component && component.fields) {
        // Store the referenced component fields
        setReferencedComponents(prev => ({
          ...prev,
          [tabId]: component.fields
        }));

        // Add a new tab if it doesn't exist
        const tabExists = tabs.some(tab => tab.id === tabId);

        if (!tabExists) {
          setTabs(prev => [
            ...prev,
            {
              id: tabId,
              title: fieldName,
              fieldName: camelCaseFieldName,
              parentPath: parentPath,
              componentType: selectedValue
            }
          ]);
        }

        // Update breadcrumbs
        const newBreadcrumb = {
          id: tabId,
          title: `${fieldName}: ${selectedValue}`,
          parentPath: parentPath
        };

        setBreadcrumbs(prev => {
          // Find if we're already in this branch
          const existingIndex = prev.findIndex(crumb => crumb.id === tabId);

          if (existingIndex >= 0) {
            // If we're already in this branch, truncate and add
            return [...prev.slice(0, existingIndex), newBreadcrumb];
          } else {
            // Otherwise add to the current breadcrumb path
            return [...prev, newBreadcrumb];
          }
        });

        // Switch to the new tab
        setActiveTab(tabId);
      }
    } catch (error) {
      console.error(`Error fetching referenced component ${selectedValue}:`, error);
    }
  };

  // Render a field input based on its type
  const renderFieldInput = (field, propName, fieldName, value, handleChange, parentPath = []) => {
    switch (field.type) {
      case 'choice':
        return (
          <select
            value={value || field.default || ''}
            onChange={(e) => handleChange(propName, e.target.value)}
          >
            {field.options.map(option => (
              <option key={option} value={option}>{option}</option>
            ))}
          </select>
        );

      case 'object-list':
        if (field.options && field.options.length > 0) {
          return (
            <div className="object-list-field">
              <select
                value={value || ''}
                onChange={(e) => {
                  const selectedValue = e.target.value;
                  handleChange(propName, selectedValue);

                  if (selectedValue) {
                    handleSelectReference(selectedValue, fieldName, parentPath);
                  }
                }}
              >
                <option value="">-- Select {field['object-list']} --</option>
                {field.options.map(option => (
                  <option key={option} value={option}>{option}</option>
                ))}
              </select>
              {value && (
                <button
                  className="btn btn-sm btn-secondary view-reference-btn"
                  onClick={() => handleSelectReference(value, fieldName, parentPath)}
                >
                  View Properties
                </button>
              )}
            </div>
          );
        }
        return (
          <input
            type="text"
            value={value || ''}
            onChange={(e) => handleChange(propName, e.target.value)}
          />
        );

      case 'real':
      case 'integer':
        return (
          <input
            type="number"
            step={field.type === 'real' ? '0.01' : '1'}
            value={value !== undefined ? value : ''}
            onChange={(e) => handleChange(propName, e.target.value)}
          />
        );

      default:
        return (
          <input
            type="text"
            value={value || ''}
            onChange={(e) => handleChange(propName, e.target.value)}
          />
        );
    }
  };

  // Group fields by category or type
  const groupFields = (fields) => {
    // This is a simplified example - you can customize the grouping logic
    const groups = {
      'Basic': [],
      'Advanced': [],
      'References': []
    };

    // Group fields based on some criteria
    Object.entries(fields).forEach(([fieldName, field]) => {
      if (field.type === 'object-list') {
        groups['References'].push({ fieldName, field });
      } else if (field.required) {
        groups['Basic'].push({ fieldName, field });
      } else {
        groups['Advanced'].push({ fieldName, field });
      }
    });

    // Filter out empty groups
    return Object.entries(groups).filter(([_, fields]) => fields.length > 0);
  };

  // Render a collapsible field group
  const renderFieldGroup = (groupName, fields, componentProps, isReadOnly = false, parentPath = []) => {
    const groupId = `${parentPath.join('.')}.${groupName}`.replace(/^\./, '');
    const isExpanded = expandedGroups[groupId] !== false; // Default to expanded

    return (
      <div className="nested-field-group" key={groupId}>
        <div
          className="field-group-header"
          onClick={() => toggleFieldGroup(groupId)}
        >
          <span className={`field-group-icon ${isExpanded ? 'expanded' : ''}`}>
            {isExpanded ? '▼' : '▶'}
          </span>
          <span className="field-group-title">{groupName}</span>
          <span className="nested-field-badge">{fields.length}</span>
        </div>

        {isExpanded && (
          <div className="field-group-content">
            {fields.map(({ fieldName, field }) => {
              // Skip the Name field for the main component as it's handled separately
              if (!isReadOnly && fieldName === 'Name') return null;

              // Convert field name to camelCase for property name
              const propName = fieldName.charAt(0).toLowerCase() + fieldName.slice(1);

              // Get the current value or use default
              const value = isReadOnly
                ? field.default
                : (componentProps[propName] !== undefined ? componentProps[propName] : field.default);

              return (
                <div className="property-field" key={fieldName}>
                  <label>
                    {fieldName}
                    {field.required && <span className="required">*</span>}
                    {field.units && <span className="units"> ({field.units})</span>}
                    {field.note && <span className="note" title={field.note}>ℹ️</span>}
                  </label>

                  {renderFieldInput(
                    field,
                    propName,
                    fieldName,
                    value,
                    isReadOnly ? () => {} : onPropertyChange,
                    [...parentPath, propName]
                  )}
                </div>
              );
            })}
          </div>
        )}
      </div>
    );
  };

  // Render fields for a component
  const renderFields = (fields, componentProps, isReadOnly = false, parentPath = []) => {
    // Group fields
    const groupedFields = groupFields(fields);

    return (
      <div className={`nested-fields-container nested-level-${parentPath.length}`}>
        {groupedFields.map(([groupName, fields]) =>
          renderFieldGroup(groupName, fields, componentProps, isReadOnly, parentPath)
        )}
      </div>
    );
  };

  if (loading) {
    return <div className="loading-properties">Loading properties...</div>;
  }

  if (!iddFields) {
    return <div className="error-message">Failed to load properties</div>;
  }

  // Render breadcrumb navigation
  const renderBreadcrumbs = () => {
    if (breadcrumbs.length <= 1) return null;

    return (
      <div className="tab-breadcrumb">
        {breadcrumbs.map((crumb, index) => (
          <div className="tab-breadcrumb-item" key={crumb.id}>
            {index < breadcrumbs.length - 1 ? (
              <span
                className="tab-breadcrumb-link"
                onClick={() => navigateToBreadcrumb(crumb.id)}
              >
                {crumb.title}
              </span>
            ) : (
              <span className="tab-breadcrumb-current">{crumb.title}</span>
            )}
          </div>
        ))}
      </div>
    );
  };

  // Render back button for nested tabs
  const renderBackButton = () => {
    if (activeTab === 'main' || breadcrumbs.length <= 1) return null;

    // Find the parent tab
    const currentTabIndex = breadcrumbs.findIndex(crumb => crumb.id === activeTab);
    if (currentTabIndex <= 0) return null;

    const parentTab = breadcrumbs[currentTabIndex - 1];

    return (
      <button
        className="btn btn-sm btn-secondary back-button"
        onClick={() => navigateToBreadcrumb(parentTab.id)}
      >
        ← Back to {parentTab.title}
      </button>
    );
  };

  return (
    <div className="tabbed-properties">
      {/* Breadcrumb navigation */}
      {renderBreadcrumbs()}

      {/* Back button for nested tabs */}
      {renderBackButton()}

      <Tabs
        activeKey={activeTab}
        onSelect={key => {
          setActiveTab(key);

          // Update breadcrumbs when switching tabs
          const tab = tabs.find(t => t.id === key);
          if (tab) {
            const tabIndex = breadcrumbs.findIndex(crumb => crumb.id === key);
            if (tabIndex >= 0) {
              // If this tab is already in breadcrumbs, truncate to this point
              setBreadcrumbs(breadcrumbs.slice(0, tabIndex + 1));
            }
          }
        }}
        className="properties-tabs"
      >
        {/* Main component tab */}
        <Tab eventKey="main" title={componentType}>
          <div className="tab-content-container">
            {renderFields(iddFields, properties, false, [])}
          </div>
        </Tab>

        {/* Referenced component tabs */}
        {tabs.filter(tab => tab.id !== 'main').map(tab => {
          const tabId = tab.id;
          const selectedValue = selectedValues[tabId];
          const parentPath = tab.parentPath || [];

          return (
            <Tab eventKey={tabId} title={tab.title} key={tabId}>
              <div className="tab-content-container">
                {selectedValue ? (
                  <>
                    <div className="selected-reference">
                      Selected: <strong>{selectedValue}</strong>
                    </div>
                    {referencedComponents[tabId] ? (
                      renderFields(
                        referencedComponents[tabId],
                        {},
                        true,
                        [...parentPath, tab.fieldName]
                      )
                    ) : (
                      <div className="loading-properties">Loading properties...</div>
                    )}
                  </>
                ) : (
                  <div className="no-selection-message">
                    No {tab.title} selected. Please select a {tab.title} from the dropdown in the main tab.
                  </div>
                )}
              </div>
            </Tab>
          );
        })}
      </Tabs>
    </div>
  );
};

export default TabbedPropertyFields;
