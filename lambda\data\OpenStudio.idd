!IDD_Version 3.5.0
!IDD_BUILD 8b81b5aaf1
! OpenStudio IDD file for HVAC components

\group HVAC

OS:AirLoopHVAC,
       \memo This object defines an air loop.
       \min-fields 4
  A1 , \field Name
       \required-field
       \type alpha
  N1 , \field DesignSupplyAirFlowRate
       \type real
       \units m3/s
       \default autosize
  A2 , \field AvailabilitySchedule
       \type object-list
       \object-list ScheduleNames
  A3 ; \field NightCycleControlType
       \type choice
       \key StayOff
       \key CycleOnAny
       \key CycleOnControlZone
       \default StayOff

OS:AirLoopHVAC:UnitarySystem,
       \memo This object models a unitary system.
       \min-fields 7
  A1 , \field Name
       \required-field
       \type alpha
  A2 , \field ControlType
       \required-field
       \type choice
       \key Load
       \key SetPoint
       \key SingleZoneVAV
       \default Load
  A3 , \field DehumidificationControlType
       \type choice
       \key None
       \key CoolReheat
       \default None
  A4 , \field FanPlacement
       \type choice
       \key BlowThrough
       \key DrawThrough
       \default BlowThrough
  A5 , \field CoolingCoil
       \type object-list
       \object-list CoolingCoilNames
  A6 , \field HeatingCoil
       \type object-list
       \object-list HeatingCoilNames
  A7 ; \field SupplyFan
       \type object-list
       \object-list FanNames

OS:Chiller:Electric:EIR,
       \memo This object models an electric chiller.
       \min-fields 7
  A1 , \field Name
       \required-field
       \type alpha
  N1 , \field ReferenceCapacity
       \type real
       \units W
       \default autosize
  N2 , \field ReferenceCOP
       \required-field
       \type real
       \units W/W
  N3 , \field MinimumPartLoadRatio
       \type real
       \minimum 0.0
       \maximum 1.0
       \default 0.1
  N4 , \field MaximumPartLoadRatio
       \type real
       \minimum 0.0
       \default 1.0
  N5 , \field OptimumPartLoadRatio
       \type real
       \minimum 0.0
       \default 1.0
  A2 ; \field CondenserType
       \type choice
       \key AirCooled
       \key WaterCooled
       \key EvaporativelyCooled
       \default WaterCooled

OS:Boiler:HotWater,
       \memo This object models a hot water boiler.
       \min-fields 6
  A1 , \field Name
       \required-field
       \type alpha
  N1 , \field NominalCapacity
       \type real
       \units W
       \default autosize
  A2 , \field FuelType
       \required-field
       \type choice
       \key NaturalGas
       \key Electricity
       \key PropaneGas
       \key FuelOil#1
       \key FuelOil#2
       \key Coal
       \key Diesel
       \key Gasoline
       \key OtherFuel1
       \key OtherFuel2
       \default NaturalGas
  N2 , \field NominalThermalEfficiency
       \type real
       \minimum 0.0
       \maximum 1.0
       \default 0.8
  N3 , \field DesignWaterOutletTemperature
       \type real
       \units C
       \default 82.0
  N4 ; \field MaximumPartLoadRatio
       \type real
       \minimum 0.0
       \default 1.0

OS:CoolingTower:SingleSpeed,
       \memo This object models a single-speed cooling tower.
       \min-fields 5
  A1 , \field Name
       \required-field
       \type alpha
  N1 , \field DesignWaterFlowRate
       \type real
       \units m3/s
       \default autosize
  N2 , \field DesignAirFlowRate
       \type real
       \units m3/s
       \default autosize
  N3 , \field DesignFanPower
       \type real
       \units W
       \default autosize
  A2 ; \field PerformanceInputMethod
       \type choice
       \key NominalCapacity
       \key UFactorTimesAreaAndDesignWaterFlowRate
       \default NominalCapacity

OS:Pump:VariableSpeed,
       \memo This object models a variable-speed pump.
       \min-fields 7
  A1 , \field Name
       \required-field
       \type alpha
  N1 , \field RatedFlowRate
       \type real
       \units m3/s
       \default autosize
  N2 , \field RatedPumpHead
       \type real
       \units Pa
       \default 179352.0
  N3 , \field RatedPowerConsumption
       \type real
       \units W
       \default autosize
  N4 , \field MotorEfficiency
       \type real
       \minimum 0.0
       \maximum 1.0
       \default 0.9
  N5 , \field FractionofMotorInefficienciestoFluidStream
       \type real
       \minimum 0.0
       \maximum 1.0
       \default 0.0
  N6 ; \field Coefficient1ofthePartLoadPerformanceCurve
       \type real
       \default 0.0

OS:AirTerminal:SingleDuct:VAV:Reheat,
       \memo This object models a VAV terminal unit with reheat.
       \min-fields 8
  A1 , \field Name
       \required-field
       \type alpha
  A2 , \field AvailabilitySchedule
       \type object-list
       \object-list ScheduleNames
  N1 , \field MaximumAirFlowRate
       \type real
       \units m3/s
       \default autosize
  N2 , \field ZoneMinimumAirFlowMethod
       \type choice
       \key Constant
       \key FixedFlowRate
       \key Scheduled
       \default Constant
  N3 , \field ConstantMinimumAirFlowFraction
       \type real
       \minimum 0.0
       \default 0.3
  A3 , \field ReheatCoil
       \type object-list
       \object-list HeatingCoilNames
  N4 , \field MaximumHotWaterOrSteamFlowRate
       \type real
       \units m3/s
       \default autosize
  N5 ; \field MinimumHotWaterOrSteamFlowRate
       \type real
       \units m3/s
       \default 0.0

OS:ThermalZone,
       \memo This object defines a thermal zone.
       \min-fields 3
  A1 , \field Name
       \required-field
       \type alpha
  N1 , \field Multiplier
       \type integer
       \minimum 1
       \default 1
  N2 , \field CeilingHeight
       \type real
       \units m
       \default autosize
  N3 ; \field Volume
       \type real
       \units m3
       \default autosize

OS:PlantLoop,
       \memo This object defines a plant loop.
       \min-fields 8
  A1 , \field Name
       \required-field
       \type alpha
  A2 , \field FluidType
       \type choice
       \key Water
       \key Steam
       \key UserDefinedFluidType
       \default Water
  A3 , \field LoopTemperatureSetpointNodeName
       \type node
  N1 , \field MaximumLoopTemperature
       \type real
       \units C
       \default 82.0
  N2 , \field MinimumLoopTemperature
       \type real
       \units C
       \default 5.0
  N3 , \field MaximumLoopFlowRate
       \type real
       \units m3/s
       \default autosize
  N4 , \field MinimumLoopFlowRate
       \type real
       \units m3/s
       \default 0.0
  A4 ; \field LoadDistributionScheme
       \type choice
       \key Optimal
       \key SequentialLoad
       \key UniformLoad
       \key UniformPLR
       \key SequentialUniformPLR
       \default SequentialLoad

OS:Connector:Mixer,
       \memo This object defines a fluid mixer.
       \min-fields 1
  A1 ; \field Name
       \required-field
       \type alpha

OS:Connector:Splitter,
       \memo This object defines a fluid splitter.
       \min-fields 1
  A1 ; \field Name
       \required-field
       \type alpha

OS:AirLoopHVAC:ZoneMixer,
       \memo This object defines a zone mixer.
       \min-fields 1
  A1 ; \field Name
       \required-field
       \type alpha

OS:AirLoopHVAC:ZoneSplitter,
       \memo This object defines a zone splitter.
       \min-fields 1
  A1 ; \field Name
       \required-field
       \type alpha

OS:AirLoopHVAC:OutdoorAirSystem,
       \memo This object defines an outdoor air system.
       \min-fields 2
  A1 , \field Name
       \required-field
       \type alpha
  A2 ; \field ControllerName
       \required-field
       \type alpha

OS:Fan:ConstantVolume,
       \memo This object defines a constant volume fan.
       \min-fields 5
  A1 , \field Name
       \required-field
       \type alpha
  N1 , \field FanEfficiency
       \required-field
       \type real
       \minimum 0.0
       \maximum 1.0
       \default 0.7
  N2 , \field PressureRise
       \required-field
       \type real
       \units Pa
       \default 500
  N3 , \field MotorEfficiency
       \required-field
       \type real
       \minimum 0.0
       \maximum 1.0
       \default 0.9
  N4 ; \field MaximumFlowRate
       \type real
       \units m3/s
       \default autosize

OS:Fan:VariableVolume,
       \memo This object defines a variable volume fan.
       \min-fields 7
  A1 , \field Name
       \required-field
       \type alpha
  N1 , \field FanEfficiency
       \required-field
       \type real
       \minimum 0.0
       \maximum 1.0
       \default 0.7
  N2 , \field PressureRise
       \required-field
       \type real
       \units Pa
       \default 500
  N3 , \field MotorEfficiency
       \required-field
       \type real
       \minimum 0.0
       \maximum 1.0
       \default 0.9
  N4 , \field MaximumFlowRate
       \type real
       \units m3/s
       \default autosize
  A2 , \field FanPowerMinimumFlowRateInputMethod
       \type choice
       \key Fraction
       \key FixedFlowRate
       \default Fraction
  N5 ; \field FanPowerMinimumFlowFraction
       \type real
       \default 0.25

OS:Coil:Cooling:Water,
       \memo This object defines a water cooling coil.
       \min-fields 5
  A1 , \field Name
       \required-field
       \type alpha
  N1 , \field DesignWaterFlowRate
       \type real
       \units m3/s
       \default autosize
  N2 , \field DesignAirFlowRate
       \type real
       \units m3/s
       \default autosize
  N3 , \field DesignInletWaterTemperature
       \type real
       \units C
       \default 7.0
  N4 , \field DesignInletAirTemperature
       \type real
       \units C
       \default 25.0
  N5 ; \field DesignOutletAirTemperature
       \type real
       \units C
       \default 10.0

OS:Coil:Heating:Water,
       \memo This object defines a water heating coil.
       \min-fields 5
  A1 , \field Name
       \required-field
       \type alpha
  N1 , \field DesignWaterFlowRate
       \type real
       \units m3/s
       \default autosize
  N2 , \field DesignAirFlowRate
       \type real
       \units m3/s
       \default autosize
  N3 , \field DesignInletWaterTemperature
       \type real
       \units C
       \default 60.0
  N4 , \field DesignInletAirTemperature
       \type real
       \units C
       \default 10.0
  N5 ; \field DesignOutletAirTemperature
       \type real
       \units C
       \default 40.0
