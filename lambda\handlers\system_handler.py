import json
import logging
import os
import uuid
import boto3
from datetime import datetime

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Initialize DynamoDB client
dynamodb = boto3.resource('dynamodb')
table = dynamodb.Table(os.environ['SYSTEMS_TABLE'])

def save_system(event, context):
    """Save an HVAC system design"""
    try:
        # Parse request body
        body = json.loads(event['body'])
        
        # Generate a unique ID if not provided
        system_id = body.get('id', str(uuid.uuid4()))
        
        # Create item to save
        item = {
            'id': system_id,
            'name': body.get('name', 'Untitled System'),
            'nodes': body.get('nodes', []),
            'edges': body.get('edges', []),
            'createdAt': body.get('timestamp', datetime.now().isoformat()),
            'updatedAt': datetime.now().isoformat()
        }
        
        # Save to DynamoDB
        table.put_item(Item=item)
        
        return {
            'statusCode': 200,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps({
                'success': True,
                'id': system_id
            })
        }
    except Exception as e:
        logger.error(f"Error saving system: {str(e)}")
        return {
            'statusCode': 500,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps({
                'success': False,
                'error': str(e)
            })
        }

def get_system(event, context):
    """Get an HVAC system design by ID"""
    try:
        # Get system ID from path parameter
        system_id = event['pathParameters']['id']
        
        # Get item from DynamoDB
        response = table.get_item(Key={'id': system_id})
        
        # Check if item exists
        if 'Item' not in response:
            return {
                'statusCode': 404,
                'headers': {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                },
                'body': json.dumps({
                    'success': False,
                    'error': 'System not found'
                })
            }
        
        return {
            'statusCode': 200,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps({
                'success': True,
                'system': response['Item']
            })
        }
    except Exception as e:
        logger.error(f"Error getting system: {str(e)}")
        return {
            'statusCode': 500,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps({
                'success': False,
                'error': str(e)
            })
        }

def list_systems(event, context):
    """List all HVAC system designs"""
    try:
        # Scan DynamoDB table
        response = table.scan()
        
        # Extract items
        systems = response.get('Items', [])
        
        # Sort by updatedAt (newest first)
        systems.sort(key=lambda x: x.get('updatedAt', ''), reverse=True)
        
        return {
            'statusCode': 200,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps({
                'success': True,
                'systems': systems
            })
        }
    except Exception as e:
        logger.error(f"Error listing systems: {str(e)}")
        return {
            'statusCode': 500,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps({
                'success': False,
                'error': str(e)
            })
        }

def delete_system(event, context):
    """Delete an HVAC system design by ID"""
    try:
        # Get system ID from path parameter
        system_id = event['pathParameters']['id']
        
        # Delete item from DynamoDB
        table.delete_item(Key={'id': system_id})
        
        return {
            'statusCode': 200,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps({
                'success': True
            })
        }
    except Exception as e:
        logger.error(f"Error deleting system: {str(e)}")
        return {
            'statusCode': 500,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps({
                'success': False,
                'error': str(e)
            })
        }
