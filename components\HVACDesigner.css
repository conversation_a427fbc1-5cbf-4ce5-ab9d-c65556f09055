.hvac-designer {
  display: flex;
  height: calc(100vh - 60px);
  width: 100%;
  overflow: hidden; /* Prevent scrollbars during resize */
}

.flow-container {
  flex: 1;
  height: 100%;
  position: relative;
  display: flex;
  min-width: 0; /* Allow container to shrink below default size */
}

.component-panel {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
  height: 100%;
}

.react-flow__node-custom {
  padding: 10px;
  border-radius: 5px;
  width: 150px;
  font-size: 12px;
  color: #222;
  text-align: center;
  border-width: 1px;
  border-style: solid;
}

.react-flow__handle {
  width: 10px;
  height: 10px;
  border-radius: 50%;
}

.react-flow__attribution {
  display: none;
}
