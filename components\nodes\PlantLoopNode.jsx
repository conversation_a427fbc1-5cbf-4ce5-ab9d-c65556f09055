import React, { useState, useMemo } from 'react';
import { <PERSON>le, Position, useReactFlow } from 'reactflow';
import { useHVACStore } from '../../store/hvacStore';
import { plantLoopConfig } from './PlantLoopConfig';

const PlantLoopNode = ({ data, selected, id }) => {
  const { label, properties } = data;
  const [showComponentMenu, setShowComponentMenu] = useState(false); // false, 'supply', or 'demand'
  const [showDropZone, setShowDropZone] = useState(false);
  const { addNode, onConnect, removeNode, nodes } = useHVACStore();
  const reactFlowInstance = useReactFlow();

  // Get the display properties (the ones to show in the node)
  const displayProps = plantLoopConfig.displayProperties || [];

  // State to track which side is being dragged over
  const [dragOverSide, setDragOverSide] = useState(null);

  // Calculate child components for each side
  const childComponents = useMemo(() => {
    const supplyChildren = nodes.filter(node =>
      node.parentId === id && node.data.side === 'supply'
    ).sort((a, b) => a.position.x - b.position.x);

    const demandChildren = nodes.filter(node =>
      node.parentId === id && node.data.side === 'demand'
    ).sort((a, b) => a.position.x - b.position.x);

    return { supply: supplyChildren, demand: demandChildren };
  }, [nodes, id]);

  // Calculate dynamic width based on child components
  const dynamicWidth = useMemo(() => {
    const maxSupplyChildren = childComponents.supply.length;
    const maxDemandChildren = childComponents.demand.length;
    const maxChildren = Math.max(maxSupplyChildren, maxDemandChildren);

    // Base width + space for each component + padding
    const baseWidth = 600;
    const componentWidth = 120; // Approximate width of each component
    const spacing = 30; // Space between components
    const padding = 100; // Extra padding for drop zones

    return Math.max(baseWidth, baseWidth + (maxChildren * (componentWidth + spacing)) + padding);
  }, [childComponents]);

  // Handle drag over to show drop zone for supply side
  const handleSupplyDragOver = (event) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
    setShowDropZone(true);
    setDragOverSide('supply');
  };

  // Handle drag over to show drop zone for demand side
  const handleDemandDragOver = (event) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
    setShowDropZone(true);
    setDragOverSide('demand');
  };

  // Handle drag leave to hide drop zone
  const handleDragLeave = () => {
    setShowDropZone(false);
    setDragOverSide(null);
  };

  // Handle deleting a child component
  const handleDeleteComponent = (componentId) => {
    if (window.confirm('Are you sure you want to delete this component?')) {
      removeNode(componentId);
    }
  };

  // Handle reordering components within a side
  const handleReorderComponent = (componentId, newPosition, side) => {
    // This would involve updating the position of the component
    // For now, we'll implement a simple left/right move
    const sideComponents = childComponents[side];
    const componentIndex = sideComponents.findIndex(comp => comp.id === componentId);

    if (componentIndex === -1) return;

    const component = sideComponents[componentIndex];
    let newX = component.position.x;

    if (newPosition === 'left' && componentIndex > 0) {
      // Move left - swap with previous component
      const prevComponent = sideComponents[componentIndex - 1];
      newX = prevComponent.position.x - 150;
    } else if (newPosition === 'right' && componentIndex < sideComponents.length - 1) {
      // Move right - swap with next component
      const nextComponent = sideComponents[componentIndex + 1];
      newX = nextComponent.position.x + 150;
    }

    // Update the component position
    const updatedNodes = nodes.map(node => {
      if (node.id === componentId) {
        return { ...node, position: { ...node.position, x: newX } };
      }
      return node;
    });

    // Update the store with new positions
    useHVACStore.setState({ nodes: updatedNodes });
  };

  // Handle adding a component from the menu
  const handleAddComponent = (componentType, side) => {
    setShowComponentMenu(false);

    // Calculate position for the new node inside the parent based on the side
    // Get existing nodes on this side to determine position
    const existingNodes = nodes.filter(node =>
      node.parentId === id &&
      node.data.side === side
    );

    let position;
    if (existingNodes.length > 0) {
      // If there are existing nodes, position the new one to the right of the rightmost node
      const rightmostNode = existingNodes.reduce((rightmost, node) => {
        return (node.position.x > rightmost.position.x) ? node : rightmost;
      }, existingNodes[0]);

      position = {
        x: rightmostNode.position.x + 150, // 150px to the right of the rightmost node
        y: side === 'supply' ? 80 : 280     // Y position based on side
      };
    } else {
      // If no existing nodes, position at the start of the side
      position = {
        x: 50,  // Start position
        y: side === 'supply' ? 80 : 280     // Y position based on side
      };
    }

    console.log(`Adding ${componentType} to ${side} side at position:`, position);

    // Create a new node of the selected component type with the parent ID set to this node's ID
    // Also include the side information in the node data
    const newNode = addNode(componentType, position, id, side);

    // Connect the new node based on which side it's on
    // Use the existing nodes from the store
    const sideChildNodes = nodes.filter(node =>
      node.parentId === id &&
      node.data.side === side
    );

    // Connect the new node to the appropriate parent or sibling
    if (side === 'supply') {
      if (sideChildNodes.length === 1) {
        // If this is the first child on the supply side, connect it to the PlantLoop's supply outlet
        onConnect({
          source: id,
          target: newNode.id,
          sourceHandle: 'supply-outlet',
          targetHandle: 'inlet'
        });
      } else if (sideChildNodes.length > 1) {
        // If there are other children on the supply side, connect to the last added child
        const lastChild = sideChildNodes[sideChildNodes.length - 2]; // -2 because the new node is already in the array
        onConnect({
          source: lastChild.id,
          target: newNode.id,
          sourceHandle: 'outlet',
          targetHandle: 'inlet'
        });
      }
    } else if (side === 'demand') {
      if (sideChildNodes.length === 1) {
        // If this is the first child on the demand side, connect it to the PlantLoop's demand inlet
        onConnect({
          source: id,
          target: newNode.id,
          sourceHandle: 'demand-inlet',
          targetHandle: 'inlet'
        });
      } else if (sideChildNodes.length > 1) {
        // If there are other children on the demand side, connect to the last added child
        const lastChild = sideChildNodes[sideChildNodes.length - 2]; // -2 because the new node is already in the array
        onConnect({
          source: lastChild.id,
          target: newNode.id,
          sourceHandle: 'outlet',
          targetHandle: 'inlet'
        });
      }
    }
  };

  // Handle drop to add component inside the PlantLoop
  const handleDrop = (event) => {
    event.preventDefault();
    setShowDropZone(false);

    // Get the component type from the drag data
    const componentType = event.dataTransfer.getData('application/reactflow');

    if (!componentType) {
      return;
    }

    // Check if the component is allowed on this side
    const side = dragOverSide || 'supply'; // Default to supply side if not specified

    // Get the component type with OS: prefix for checking against allowed components
    let fullComponentType;
    if (componentType === 'chiller') {
      fullComponentType = 'OS:Chiller:Electric:EIR';
    } else if (componentType === 'boiler') {
      fullComponentType = 'OS:Boiler:HotWater';
    } else if (componentType === 'coolingTower') {
      fullComponentType = 'OS:CoolingTower:SingleSpeed';
    } else if (componentType === 'pump') {
      fullComponentType = 'OS:Pump:VariableSpeed';
    } else if (componentType === 'connectorMixer') {
      fullComponentType = 'OS:Connector:Mixer';
    } else if (componentType === 'connectorSplitter') {
      fullComponentType = 'OS:Connector:Splitter';
    } else {
      fullComponentType = `OS:${componentType}`;
    }

    const allowedOnSide = plantLoopConfig.allowedComponents.includes(fullComponentType);

    if (!allowedOnSide) {
      alert(`${componentType} cannot be added to the PlantLoop.`);
      setDragOverSide(null);
      return;
    }

    // Calculate position for the new node inside the parent based on the side
    // Get existing nodes on this side to determine position
    const existingNodes = nodes.filter(node =>
      node.parentId === id &&
      node.data.side === side
    );

    let position;
    if (existingNodes.length > 0) {
      // If there are existing nodes, position the new one to the right of the rightmost node
      const rightmostNode = existingNodes.reduce((rightmost, node) => {
        return (node.position.x > rightmost.position.x) ? node : rightmost;
      }, existingNodes[0]);

      position = {
        x: rightmostNode.position.x + 150, // 150px to the right of the rightmost node
        y: side === 'supply' ? 80 : 280     // Y position based on side
      };
    } else {
      // Default positions if no existing nodes
      if (side === 'supply') {
        position = {
          x: 50,  // Position inside the parent (relative to parent's coordinate system)
          y: 80   // Position in the supply side
        };
      } else {
        position = {
          x: 50,  // Position inside the parent (relative to parent's coordinate system)
          y: 280  // Position in the demand side
        };
      }
    }

    console.log(`Adding ${componentType} to ${side} side at position:`, position);

    // Create a new node of the selected component type with the parent ID set to this node's ID
    // Also include the side information in the node data
    const newNode = addNode(componentType, position, id, side);

    // Connect the new node based on which side it's on
    // Use the existing nodes from the store
    const sideChildNodes = nodes.filter(node =>
      node.parentId === id &&
      node.data.side === side
    );

    if (side === 'supply') {
      if (sideChildNodes.length === 1) {
        // If this is the first child on the supply side, connect it to the PlantLoop's supply outlet
        onConnect({
          source: id,
          target: newNode.id,
          sourceHandle: 'supply-outlet',
          targetHandle: 'inlet'
        });
      } else if (sideChildNodes.length > 1) {
        // If there are other children on the supply side, connect to the last added child
        const lastChild = sideChildNodes[sideChildNodes.length - 2]; // -2 because the new node is already in the array
        onConnect({
          source: lastChild.id,
          target: newNode.id,
          sourceHandle: 'outlet',
          targetHandle: 'inlet'
        });
      }
    } else if (side === 'demand') {
      if (sideChildNodes.length === 1) {
        // If this is the first child on the demand side, connect it to the PlantLoop's demand inlet
        onConnect({
          source: id,
          target: newNode.id,
          sourceHandle: 'demand-inlet',
          targetHandle: 'inlet'
        });
      } else if (sideChildNodes.length > 1) {
        // If there are other children on the demand side, connect to the last added child
        const lastChild = sideChildNodes[sideChildNodes.length - 2]; // -2 because the new node is already in the array
        onConnect({
          source: lastChild.id,
          target: newNode.id,
          sourceHandle: 'outlet',
          targetHandle: 'inlet'
        });
      }
    }

    // Reset the drag over side
    setDragOverSide(null);
  };

  return (
    <div
      className={`plant-loop-node parent-node ${selected ? 'selected' : ''}`}
      style={{ width: `${dynamicWidth}px` }}
    >
      {/* Input handles */}
      {plantLoopConfig.inputs.map((input, index) => (
        <Handle
          key={`input-${index}`}
          type="target"
          position={input.position}
          id={input.id}
          className={`handle ${input.className}`}
          style={input.style}
        />
      ))}

      <div className="node-content">
        <div className="node-header">
          <div className="node-title">{label}</div>
          <div className="node-subtitle">Plant Loop with Supply and Demand Sides</div>
        </div>

        {/* Supply Side */}
        <div
          className={`node-side supply-side ${dragOverSide === 'supply' ? 'drop-zone-active' : ''}`}
          onDragOver={handleSupplyDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <div className="side-header">Supply Side</div>
          <div className="side-content">
            {/* Drop zone indicator for supply side */}
            {showDropZone && dragOverSide === 'supply' && (
              <div className="drop-zone-indicator">
                Drop component here to add to Supply Side
              </div>
            )}

            {/* Render child components for supply side */}
            <div className="child-components-container">
              {childComponents.supply.map((component, index) => (
                <div key={component.id} className="child-component-wrapper">
                  <div className="child-component-controls">
                    {index > 0 && (
                      <button
                        className="component-control-btn reorder-btn"
                        onClick={() => handleReorderComponent(component.id, 'left', 'supply')}
                        title="Move Left"
                      >
                        ←
                      </button>
                    )}
                    {index < childComponents.supply.length - 1 && (
                      <button
                        className="component-control-btn reorder-btn"
                        onClick={() => handleReorderComponent(component.id, 'right', 'supply')}
                        title="Move Right"
                      >
                        →
                      </button>
                    )}
                    <button
                      className="component-control-btn delete-btn"
                      onClick={() => handleDeleteComponent(component.id)}
                      title="Delete Component"
                    >
                      ×
                    </button>
                  </div>
                  <div className="child-component-preview">
                    {component.data.label || component.type}
                  </div>
                </div>
              ))}
            </div>

            {/* Add Component Button for Supply Side */}
            <div className="add-component-container">
              <button
                className="add-component-button supply-button"
                onClick={() => setShowComponentMenu('supply')}
              >
                Add to Supply Side
              </button>
            </div>
          </div>
        </div>

        {/* Divider between supply and demand sides */}
        <div className="side-divider"></div>

        {/* Demand Side */}
        <div
          className={`node-side demand-side ${dragOverSide === 'demand' ? 'drop-zone-active' : ''}`}
          onDragOver={handleDemandDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <div className="side-header">Demand Side</div>
          <div className="side-content">
            {/* Drop zone indicator for demand side */}
            {showDropZone && dragOverSide === 'demand' && (
              <div className="drop-zone-indicator">
                Drop component here to add to Demand Side
              </div>
            )}

            {/* Render child components for demand side */}
            <div className="child-components-container">
              {childComponents.demand.map((component, index) => (
                <div key={component.id} className="child-component-wrapper">
                  <div className="child-component-controls">
                    {index > 0 && (
                      <button
                        className="component-control-btn reorder-btn"
                        onClick={() => handleReorderComponent(component.id, 'left', 'demand')}
                        title="Move Left"
                      >
                        ←
                      </button>
                    )}
                    {index < childComponents.demand.length - 1 && (
                      <button
                        className="component-control-btn reorder-btn"
                        onClick={() => handleReorderComponent(component.id, 'right', 'demand')}
                        title="Move Right"
                      >
                        →
                      </button>
                    )}
                    <button
                      className="component-control-btn delete-btn"
                      onClick={() => handleDeleteComponent(component.id)}
                      title="Delete Component"
                    >
                      ×
                    </button>
                  </div>
                  <div className="child-component-preview">
                    {component.data.label || component.type}
                  </div>
                </div>
              ))}
            </div>

            {/* Add Component Button for Demand Side */}
            <div className="add-component-container">
              <button
                className="add-component-button demand-button"
                onClick={() => setShowComponentMenu('demand')}
              >
                Add to Demand Side
              </button>
            </div>
          </div>
        </div>

        {/* Component Menu */}
        {showComponentMenu && (
          <div className="component-menu">
            <div className="component-menu-header">
              Select Component for {showComponentMenu === 'supply' ? 'Supply Side' : 'Demand Side'}
            </div>
            <div className="component-menu-items">
              {showComponentMenu === 'supply' ? (
                <>
                  <div
                    className="component-menu-item"
                    onClick={() => handleAddComponent('pump', 'supply')}
                  >
                    Pump
                  </div>
                  <div
                    className="component-menu-item"
                    onClick={() => handleAddComponent('chiller', 'supply')}
                  >
                    Chiller
                  </div>
                  <div
                    className="component-menu-item"
                    onClick={() => handleAddComponent('boiler', 'supply')}
                  >
                    Boiler
                  </div>
                  <div
                    className="component-menu-item"
                    onClick={() => handleAddComponent('coolingTower', 'supply')}
                  >
                    Cooling Tower
                  </div>
                </>
              ) : (
                <>
                  <div
                    className="component-menu-item"
                    onClick={() => handleAddComponent('pump', 'demand')}
                  >
                    Pump
                  </div>
                  <div
                    className="component-menu-item"
                    onClick={() => handleAddComponent('coilCoolingWater', 'demand')}
                  >
                    Cooling Coil
                  </div>
                  <div
                    className="component-menu-item"
                    onClick={() => handleAddComponent('coilHeatingWater', 'demand')}
                  >
                    Heating Coil
                  </div>
                </>
              )}
              <div
                className="component-menu-item cancel-item"
                onClick={() => setShowComponentMenu(false)}
              >
                Cancel
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Output handles */}
      {plantLoopConfig.outputs.map((output, index) => (
        <Handle
          key={`output-${index}`}
          type="source"
          position={output.position}
          id={output.id}
          className={`handle ${output.className}`}
          style={output.style}
        />
      ))}
    </div>
  );
};

export default React.memo(PlantLoopNode);
