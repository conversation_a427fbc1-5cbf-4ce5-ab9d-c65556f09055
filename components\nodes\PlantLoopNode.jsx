import React, { useState, useMemo } from 'react';
import { <PERSON>le, Position, useReactFlow } from 'reactflow';
import { useHVACStore } from '../../store/hvacStore';
import { plantLoopConfig } from './PlantLoopConfig';

const PlantLoopNode = ({ data, selected, id }) => {
  const { label, properties } = data;
  const [showComponentMenu, setShowComponentMenu] = useState(false); // false, 'supply', or 'demand'
  const [showDropZone, setShowDropZone] = useState(false);
  const { addNode, onConnect, removeNode, nodes } = useHVACStore();
  const reactFlowInstance = useReactFlow();

  // Get the display properties (the ones to show in the node)
  const displayProps = plantLoopConfig.displayProperties || [];

  // State to track which side is being dragged over
  const [dragOverSide, setDragOverSide] = useState(null);

  // Calculate child components for each side
  const childComponents = useMemo(() => {
    const supplyChildren = nodes.filter(node =>
      node.parentId === id && node.data.side === 'supply'
    ).sort((a, b) => a.position.x - b.position.x);

    const demandChildren = nodes.filter(node =>
      node.parentId === id && node.data.side === 'demand'
    ).sort((a, b) => a.position.x - b.position.x);

    return { supply: supplyChildren, demand: demandChildren };
  }, [nodes, id]);

  // Calculate dynamic width based on child components
  const dynamicWidth = useMemo(() => {
    const maxSupplyChildren = childComponents.supply.length;
    const maxDemandChildren = childComponents.demand.length;
    const maxChildren = Math.max(maxSupplyChildren, maxDemandChildren);

    // Base width + space for each component + padding
    const baseWidth = 600;
    const componentWidth = 120; // Approximate width of each component
    const spacing = 30; // Space between components
    const padding = 100; // Extra padding for drop zones

    return Math.max(baseWidth, baseWidth + (maxChildren * (componentWidth + spacing)) + padding);
  }, [childComponents]);

  // Handle drag over to show drop zone for supply side
  const handleSupplyDragOver = (event) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
    setShowDropZone(true);
    setDragOverSide('supply');
  };

  // Handle drag over to show drop zone for demand side
  const handleDemandDragOver = (event) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
    setShowDropZone(true);
    setDragOverSide('demand');
  };

  // Handle drag leave to hide drop zone
  const handleDragLeave = () => {
    setShowDropZone(false);
    setDragOverSide(null);
  };

  // Handle drop to add component inside the PlantLoop
  const handleDrop = (event) => {
    event.preventDefault();
    setShowDropZone(false);

    // Get the component type from the drag data
    const componentType = event.dataTransfer.getData('application/reactflow');

    if (!componentType) {
      return;
    }

    // Check if the component is allowed on this side
    const side = dragOverSide || 'supply'; // Default to supply side if not specified

    // Get the component type with OS: prefix for checking against allowed components
    let fullComponentType;
    if (componentType === 'chiller') {
      fullComponentType = 'OS:Chiller:Electric:EIR';
    } else if (componentType === 'boiler') {
      fullComponentType = 'OS:Boiler:HotWater';
    } else if (componentType === 'coolingTower') {
      fullComponentType = 'OS:CoolingTower:SingleSpeed';
    } else if (componentType === 'pump') {
      fullComponentType = 'OS:Pump:VariableSpeed';
    } else if (componentType === 'connectorMixer') {
      fullComponentType = 'OS:Connector:Mixer';
    } else if (componentType === 'connectorSplitter') {
      fullComponentType = 'OS:Connector:Splitter';
    } else {
      fullComponentType = `OS:${componentType}`;
    }

    const allowedOnSide = plantLoopConfig.allowedComponents.includes(fullComponentType);

    if (!allowedOnSide) {
      alert(`${componentType} cannot be added to the PlantLoop.`);
      setDragOverSide(null);
      return;
    }

    // Calculate position for the new node inside the parent based on the side
    // Get existing nodes on this side to determine position
    const existingNodes = nodes.filter(node =>
      node.parentId === id &&
      node.data.side === side
    );

    let position;
    if (existingNodes.length > 0) {
      // If there are existing nodes, position the new one to the right of the rightmost node
      const rightmostNode = existingNodes.reduce((rightmost, node) => {
        return (node.position.x > rightmost.position.x) ? node : rightmost;
      }, existingNodes[0]);

      position = {
        x: rightmostNode.position.x + 150, // 150px to the right of the rightmost node
        y: side === 'supply' ? 80 : 280     // Y position based on side
      };
    } else {
      // Default positions if no existing nodes
      if (side === 'supply') {
        position = {
          x: 50,  // Position inside the parent (relative to parent's coordinate system)
          y: 80   // Position in the supply side
        };
      } else {
        position = {
          x: 50,  // Position inside the parent (relative to parent's coordinate system)
          y: 280  // Position in the demand side
        };
      }
    }

    console.log(`Adding ${componentType} to ${side} side at position:`, position);

    // Create a new node of the selected component type with the parent ID set to this node's ID
    // Also include the side information in the node data
    const newNode = addNode(componentType, position, id, side);

    // Connect the new node based on which side it's on
    // Use the existing nodes from the store
    const sideChildNodes = nodes.filter(node =>
      node.parentId === id &&
      node.data.side === side
    );

    if (side === 'supply') {
      if (sideChildNodes.length === 1) {
        // If this is the first child on the supply side, connect it to the PlantLoop's supply outlet
        onConnect({
          source: id,
          target: newNode.id,
          sourceHandle: 'supply-outlet',
          targetHandle: 'inlet'
        });
      } else if (sideChildNodes.length > 1) {
        // If there are other children on the supply side, connect to the last added child
        const lastChild = sideChildNodes[sideChildNodes.length - 2]; // -2 because the new node is already in the array
        onConnect({
          source: lastChild.id,
          target: newNode.id,
          sourceHandle: 'outlet',
          targetHandle: 'inlet'
        });
      }
    } else if (side === 'demand') {
      if (sideChildNodes.length === 1) {
        // If this is the first child on the demand side, connect it to the PlantLoop's demand inlet
        onConnect({
          source: id,
          target: newNode.id,
          sourceHandle: 'demand-inlet',
          targetHandle: 'inlet'
        });
      } else if (sideChildNodes.length > 1) {
        // If there are other children on the demand side, connect to the last added child
        const lastChild = sideChildNodes[sideChildNodes.length - 2]; // -2 because the new node is already in the array
        onConnect({
          source: lastChild.id,
          target: newNode.id,
          sourceHandle: 'outlet',
          targetHandle: 'inlet'
        });
      }
    }

    // Reset the drag over side
    setDragOverSide(null);
  };

  return (
    <div
      className={`plant-loop-node parent-node ${selected ? 'selected' : ''}`}
    >
      {/* Input handles */}
      {plantLoopConfig.inputs.map((input, index) => (
        <Handle
          key={`input-${index}`}
          type="target"
          position={input.position}
          id={input.id}
          className={`handle ${input.className}`}
          style={input.style}
        />
      ))}

      <div className="node-content">
        <div className="node-header">
          <div className="node-title">{label}</div>
          <div className="node-subtitle">Plant Loop with Supply and Demand Sides</div>
        </div>

        {/* Supply Side */}
        <div
          className={`node-side supply-side ${dragOverSide === 'supply' ? 'drop-zone-active' : ''}`}
          onDragOver={handleSupplyDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <div className="side-header">Supply Side</div>
          <div className="side-content">
            {/* Drop zone indicator for supply side */}
            {showDropZone && dragOverSide === 'supply' && (
              <div className="drop-zone-indicator">
                Drop component here to add to Supply Side
              </div>
            )}
          </div>
        </div>

        {/* Divider between supply and demand sides */}
        <div className="side-divider"></div>

        {/* Demand Side */}
        <div
          className={`node-side demand-side ${dragOverSide === 'demand' ? 'drop-zone-active' : ''}`}
          onDragOver={handleDemandDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <div className="side-header">Demand Side</div>
          <div className="side-content">
            {/* Drop zone indicator for demand side */}
            {showDropZone && dragOverSide === 'demand' && (
              <div className="drop-zone-indicator">
                Drop component here to add to Demand Side
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Output handles */}
      {plantLoopConfig.outputs.map((output, index) => (
        <Handle
          key={`output-${index}`}
          type="source"
          position={output.position}
          id={output.id}
          className={`handle ${output.className}`}
          style={output.style}
        />
      ))}
    </div>
  );
};

export default React.memo(PlantLoopNode);
