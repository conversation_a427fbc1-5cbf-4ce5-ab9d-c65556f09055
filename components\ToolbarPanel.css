.toolbar-panel {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f5f5f5;
  position: relative;
}

.toolbar-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.divider {
  height: 24px;
  width: 1px;
  background-color: #e0e0e0;
  margin: 0 5px;
}

.status-message {
  margin-top: 10px;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
}

.status-message.success {
  background-color: #e8f5e9;
  color: #2e7d32;
  border: 1px solid #a5d6a7;
}

.status-message.error {
  background-color: #ffebee;
  color: #c62828;
  border: 1px solid #ef9a9a;
}

.save-dialog {
  position: absolute;
  top: 60px;
  right: 16px;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 16px;
  width: 300px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.save-dialog h4 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 16px;
}

.results-panel {
  position: absolute;
  top: 60px;
  right: 16px;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 0;
  width: 400px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
}

.results-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.close-button {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #666;
}

.close-button:hover {
  color: #333;
}

.results-section {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.results-section h5 {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 500;
  color: #555;
}

.result-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.result-item.total {
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px dashed #e0e0e0;
  font-weight: 500;
}

.result-label {
  color: #666;
}

.result-value {
  font-weight: 500;
}

.result-value.warning {
  color: #f57c00;
}
