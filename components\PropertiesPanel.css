.properties-panel-container {
  position: relative;
  display: flex;
  height: 100%;
}

.properties-panel {
  background-color: #f5f5f5;
  border-left: 1px solid #e0e0e0;
  padding: 16px;
  overflow-y: auto;
  flex-shrink: 0;
  position: relative;
}

.resize-handle {
  position: absolute;
  left: -5px; /* Extend slightly outside the panel for easier grabbing */
  top: 0;
  width: 10px; /* Wider area for easier grabbing */
  height: 100%;
  cursor: col-resize;
  background-color: transparent;
  transition: background-color 0.2s;
  z-index: 10;
  touch-action: none; /* Prevent scrolling on touch devices */
}

.resize-handle::before {
  content: "⋮"; /* Three vertical dots */
  position: absolute;
  left: 4px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
  font-size: 16px;
  line-height: 1;
}

.resize-handle::after {
  content: "";
  position: absolute;
  left: 7px;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: #e0e0e0;
  height: 100%;
}

.resize-handle:hover,
.resize-handle.active {
  background-color: rgba(25, 118, 210, 0.2);
}

.resize-handle:hover::after,
.resize-handle.active::after {
  background-color: #1976d2;
}

.resize-handle:hover::before,
.resize-handle.active::before {
  color: #1976d2;
}

.panel-title {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 16px;
  color: #333;
}

.component-type {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 16px;
  padding: 6px 10px;
  background-color: #e0e0e0;
  border-radius: 4px;
  display: inline-block;
}

.no-selection-message {
  color: #666;
  font-style: italic;
  margin-top: 20px;
}

.property-field {
  margin-bottom: 16px;
}

.property-field label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 6px;
  color: #555;
}

.property-field label .required {
  color: #f44336;
  margin-left: 4px;
}

.property-field label .units {
  color: #666;
  font-weight: normal;
  font-size: 12px;
}

.loading-properties {
  color: #666;
  font-style: italic;
  margin: 20px 0;
  text-align: center;
}

.property-field input,
.property-field select {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.property-field input:focus,
.property-field select:focus {
  border-color: #1976d2;
  outline: none;
}

.panel-actions {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e0e0e0;
  display: flex;
  gap: 10px;
}

.btn {
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
}

.btn-primary {
  background-color: #1976d2;
  color: white;
}

.btn-primary:hover {
  background-color: #1565c0;
}

.btn-secondary {
  background-color: #e0e0e0;
  color: #333;
}

.btn-secondary:hover {
  background-color: #d5d5d5;
}

.btn-danger {
  background-color: #f44336;
  color: white;
}

.btn-danger:hover {
  background-color: #d32f2f;
}

.status-message {
  margin-top: 10px;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
}

.status-message.success {
  background-color: #e8f5e9;
  color: #2e7d32;
  border: 1px solid #a5d6a7;
}

.status-message.error {
  background-color: #ffebee;
  color: #c62828;
  border: 1px solid #ef9a9a;
}

/* Nested fields styles */
.nested-fields-container {
  margin-left: 16px;
  border-left: 2px solid #e0e0e0;
  padding-left: 16px;
}

.nested-field-group {
  margin-bottom: 24px;
}

.nested-field-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 4px;
  border-bottom: 1px solid #e0e0e0;
}

.nested-field-title {
  font-weight: 500;
  font-size: 14px;
  color: #333;
}

.nested-field-badge {
  margin-left: 8px;
  padding: 2px 6px;
  background-color: #e0e0e0;
  border-radius: 10px;
  font-size: 12px;
  color: #666;
}

.nested-level-0 {
  /* Base level - no additional styling */
}

.nested-level-1 {
  margin-left: 16px;
  border-left: 2px solid #e0e0e0;
  padding-left: 16px;
}

.nested-level-2 {
  margin-left: 16px;
  border-left: 2px solid #b3e5fc;
  padding-left: 16px;
}

.nested-level-3 {
  margin-left: 16px;
  border-left: 2px solid #c8e6c9;
  padding-left: 16px;
}

/* Breadcrumb navigation for nested tabs */
.tab-breadcrumb {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding: 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  font-size: 12px;
}

.tab-breadcrumb-item {
  display: flex;
  align-items: center;
}

.tab-breadcrumb-item:not(:last-child)::after {
  content: "›";
  margin: 0 8px;
  color: #999;
}

.tab-breadcrumb-link {
  color: #1976d2;
  cursor: pointer;
}

.tab-breadcrumb-current {
  font-weight: 500;
  color: #333;
}

/* Collapsible field groups */
.field-group-header {
  display: flex;
  align-items: center;
  padding: 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
}

.field-group-title {
  flex: 1;
  font-weight: 500;
  font-size: 14px;
}

.field-group-icon {
  margin-right: 8px;
  transition: transform 0.2s;
}

.field-group-icon.expanded {
  transform: rotate(90deg);
}

.field-group-content {
  padding-left: 16px;
  margin-bottom: 16px;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

/* Object-list field styles */
.object-list-field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.view-reference-btn {
  align-self: flex-start;
  font-size: 12px;
  padding: 4px 8px;
}

/* Back button */
.back-button {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  padding: 4px 8px;
}

/* Selected reference */
.selected-reference {
  margin-bottom: 16px;
  padding: 8px;
  background-color: #e3f2fd;
  border-radius: 4px;
  font-size: 14px;
}
