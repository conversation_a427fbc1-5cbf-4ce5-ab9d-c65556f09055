import React from 'react';
import { Position } from 'reactflow';

// Air Handler Node Configuration
export const airHandlerConfig = {
  componentType: 'OS:AirLoopHVAC',
  nodeType: 'air-handler',
  inputs: [
    {
      id: 'chilled-water-in',
      position: Position.Left,
      className: 'chilled-water-handle',
      style: { top: '30%' }
    },
    {
      id: 'hot-water-in',
      position: Position.Left,
      className: 'hot-water-handle',
      style: { top: '70%' }
    }
  ],
  outputs: [
    {
      id: 'supply-air',
      position: Position.Right,
      className: 'air-handle',
      style: { top: '30%' }
    },
    {
      id: 'return-air',
      position: Position.Right,
      className: 'air-handle',
      style: { top: '70%' }
    },
    {
      id: 'chilled-water-out',
      position: Position.Bottom,
      className: 'chilled-water-handle',
      style: { left: '30%' }
    },
    {
      id: 'hot-water-out',
      position: Position.Bottom,
      className: 'hot-water-handle',
      style: { left: '70%' }
    }
  ],
  displayProperties: [
    {
      name: 'airflow',
      label: 'Airflow',
      unit: 'CFM'
    },
    {
      name: 'coolingCapacity',
      label: 'Cooling',
      unit: 'Tons',
      format: (value) => value / 12000
    }
  ],
  icon: (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x="2" y="4" width="20" height="16" rx="2" stroke="#64b5f6" strokeWidth="2"/>
      <path d="M6 8L18 8" stroke="#64b5f6" strokeWidth="2"/>
      <path d="M6 12L18 12" stroke="#64b5f6" strokeWidth="2"/>
      <path d="M6 16L18 16" stroke="#64b5f6" strokeWidth="2"/>
    </svg>
  )
};

// Chiller Node Configuration
export const chillerConfig = {
  componentType: 'OS:Chiller:Electric:EIR',
  nodeType: 'chiller',
  inputs: [
    {
      id: 'condenser-water-in',
      position: Position.Top,
      className: 'condenser-water-handle',
      style: { left: '30%' }
    },
    {
      id: 'chilled-water-in',
      position: Position.Left,
      className: 'chilled-water-handle',
      style: { top: '50%' }
    }
  ],
  outputs: [
    {
      id: 'chilled-water-out',
      position: Position.Right,
      className: 'chilled-water-handle',
      style: { top: '50%' }
    },
    {
      id: 'condenser-water-out',
      position: Position.Bottom,
      className: 'condenser-water-handle',
      style: { left: '70%' }
    }
  ],
  displayProperties: [
    {
      name: 'capacity',
      label: 'Capacity',
      unit: 'Tons'
    },
    {
      name: 'cop',
      label: 'COP',
      unit: ''
    }
  ],
  icon: (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x="3" y="3" width="18" height="18" rx="2" stroke="#4fc3f7" strokeWidth="2"/>
      <path d="M7 7L17 17" stroke="#4fc3f7" strokeWidth="2"/>
      <path d="M17 7L7 17" stroke="#4fc3f7" strokeWidth="2"/>
    </svg>
  )
};

// Boiler Node Configuration
export const boilerConfig = {
  componentType: 'OS:Boiler:HotWater',
  nodeType: 'boiler',
  inputs: [
    {
      id: 'hot-water-in',
      position: Position.Left,
      className: 'hot-water-handle',
      style: { top: '50%' }
    }
  ],
  outputs: [
    {
      id: 'hot-water-out',
      position: Position.Right,
      className: 'hot-water-handle',
      style: { top: '50%' }
    }
  ],
  displayProperties: [
    {
      name: 'capacity',
      label: 'Capacity',
      unit: 'BTU/h',
      format: (value) => value
    },
    {
      name: 'efficiency',
      label: 'Efficiency',
      unit: ''
    }
  ],
  icon: (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x="4" y="3" width="16" height="18" rx="2" stroke="#ff8a65" strokeWidth="2"/>
      <path d="M8 8C8 8 10 6 12 8C14 10 16 8 16 8" stroke="#ff8a65" strokeWidth="2"/>
      <path d="M8 12C8 12 10 10 12 12C14 14 16 12 16 12" stroke="#ff8a65" strokeWidth="2"/>
      <path d="M8 16C8 16 10 14 12 16C14 18 16 16 16 16" stroke="#ff8a65" strokeWidth="2"/>
    </svg>
  )
};

// Cooling Tower Node Configuration
export const coolingTowerConfig = {
  componentType: 'OS:CoolingTower:SingleSpeed',
  nodeType: 'cooling-tower',
  inputs: [
    {
      id: 'condenser-water-in',
      position: Position.Bottom,
      className: 'condenser-water-handle',
      style: { left: '50%' }
    }
  ],
  outputs: [
    {
      id: 'condenser-water-out',
      position: Position.Top,
      className: 'condenser-water-handle',
      style: { left: '50%' }
    }
  ],
  displayProperties: [
    {
      name: 'capacity',
      label: 'Capacity',
      unit: 'Tons'
    },
    {
      name: 'fanPower',
      label: 'Fan Power',
      unit: 'HP'
    }
  ],
  icon: (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M12 3L4 10H20L12 3Z" stroke="#81c784" strokeWidth="2"/>
      <rect x="5" y="10" width="14" height="10" stroke="#81c784" strokeWidth="2"/>
      <path d="M8 14H16" stroke="#81c784" strokeWidth="2"/>
      <path d="M8 18H16" stroke="#81c784" strokeWidth="2"/>
    </svg>
  )
};

// Pump Node Configuration
export const pumpConfig = {
  componentType: 'OS:Pump:VariableSpeed',
  nodeType: 'pump',
  inputs: [
    {
      id: 'water-in',
      position: Position.Left,
      className: 'water-handle',
      style: { top: '50%' }
    }
  ],
  outputs: [
    {
      id: 'water-out',
      position: Position.Right,
      className: 'water-handle',
      style: { top: '50%' }
    }
  ],
  displayProperties: [
    {
      name: 'flowRate',
      label: 'Flow Rate',
      unit: 'GPM'
    },
    {
      name: 'power',
      label: 'Power',
      unit: 'HP'
    }
  ],
  icon: (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="12" cy="12" r="6" stroke="#9575cd" strokeWidth="2"/>
      <path d="M12 6L12 18" stroke="#9575cd" strokeWidth="2"/>
      <path d="M9 9L15 15" stroke="#9575cd" strokeWidth="2"/>
      <path d="M15 9L9 15" stroke="#9575cd" strokeWidth="2"/>
    </svg>
  )
};

// VAV Node Configuration
export const vavConfig = {
  componentType: 'OS:AirTerminal:SingleDuct:VAV:Reheat',
  nodeType: 'vav',
  inputs: [
    {
      id: 'air-in',
      position: Position.Left,
      className: 'air-handle',
      style: { top: '50%' }
    },
    {
      id: 'hot-water-in',
      position: Position.Bottom,
      className: 'hot-water-handle',
      style: { left: '30%' }
    }
  ],
  outputs: [
    {
      id: 'air-out',
      position: Position.Right,
      className: 'air-handle',
      style: { top: '50%' }
    },
    {
      id: 'hot-water-out',
      position: Position.Bottom,
      className: 'hot-water-handle',
      style: { left: '70%' }
    }
  ],
  displayProperties: [
    {
      name: 'airflow',
      label: 'Airflow',
      unit: 'CFM'
    },
    {
      name: 'reheatCapacity',
      label: 'Reheat',
      unit: 'BTU/h',
      format: (value) => value
    }
  ],
  icon: (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x="3" y="6" width="18" height="12" rx="2" stroke="#7986cb" strokeWidth="2"/>
      <path d="M7 12H17" stroke="#7986cb" strokeWidth="2"/>
      <path d="M12 9L12 15" stroke="#7986cb" strokeWidth="2"/>
    </svg>
  )
};

// Zone Node Configuration
export const zoneConfig = {
  componentType: 'OS:ThermalZone',
  nodeType: 'zone',
  inputs: [
    {
      id: 'air-in',
      position: Position.Left,
      className: 'air-handle',
      style: { top: '50%' }
    }
  ],
  outputs: [
    {
      id: 'air-out',
      position: Position.Right,
      className: 'air-handle',
      style: { top: '50%' }
    }
  ],
  displayProperties: [
    {
      name: 'area',
      label: 'Area',
      unit: 'ft²'
    },
    {
      name: 'peakCoolingLoad',
      label: 'Cooling Load',
      unit: 'BTU/h',
      format: (value) => value
    }
  ],
  icon: (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x="3" y="3" width="18" height="18" rx="2" stroke="#a1887f" strokeWidth="2"/>
      <path d="M7 7H17" stroke="#a1887f" strokeWidth="2"/>
      <path d="M7 12H17" stroke="#a1887f" strokeWidth="2"/>
      <path d="M7 17H17" stroke="#a1887f" strokeWidth="2"/>
    </svg>
  )
};

export default {
  airHandlerConfig,
  chillerConfig,
  boilerConfig,
  coolingTowerConfig,
  pumpConfig,
  vavConfig,
  zoneConfig
};
