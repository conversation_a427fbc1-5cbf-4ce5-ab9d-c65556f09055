import axios from 'axios';

// Base URL for API
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

// Create axios instance
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Map our internal component types to OpenStudio component types
const componentTypeMap = {
  'airLoopHVAC': 'OS:AirLoopHVAC',
  'airHandler': 'OS:AirLoopHVAC:UnitarySystem',
  'chiller': 'OS:Chiller:Electric:EIR',
  'boiler': 'OS:Boiler:HotWater',
  'coolingTower': 'OS:CoolingTower:SingleSpeed',
  'pump': 'OS:Pump:VariableSpeed',
  'vav': 'OS:AirTerminal:SingleDuct:VAV:Reheat',
  'zone': 'OS:ThermalZone',
  'plantLoop': 'OS:PlantLoop',
  'connectorMixer': 'OS:Connector:Mixer',
  'zoneMixer': 'OS:AirLoopHVAC:ZoneMixer',
  'connectorSplitter': 'OS:Connector:Splitter',
  'zoneSplitter': 'OS:AirLoopHVAC:ZoneSplitter',
  'outdoorAirSystem': 'OS:AirLoopHVAC:OutdoorAirSystem',
  'fanConstantVolume': 'OS:Fan:ConstantVolume',
  'fanVariableVolume': 'OS:Fan:VariableVolume',
  'coilCoolingWater': 'OS:Coil:Cooling:Water',
  'coilHeatingWater': 'OS:Coil:Heating:Water'
};

// Reverse map for converting OpenStudio types to our internal types
const reverseComponentTypeMap = Object.entries(componentTypeMap).reduce((acc, [key, value]) => {
  acc[value] = key;
  return acc;
}, {});

/**
 * Get all component types from the IDD file
 * @returns {Promise<Array>} - List of component types
 */
export const getComponentTypes = async () => {
  try {
    const response = await api.get('/idd/components');
    const components = response.data.components || [];

    // Add our internal component type to each component
    return components.map(component => {
      const internalType = reverseComponentTypeMap[component.name];
      return {
        ...component,
        internalType: internalType || component.name
      };
    });
  } catch (error) {
    console.error('Error fetching component types:', error);
    throw error;
  }
};

/**
 * Get component properties for a specific component type
 * @param {string} componentType - The component type (e.g., 'airLoopHVAC')
 * @returns {Promise<Object>} - Component properties
 */
export const getComponentProperties = async (componentType) => {
  try {
    // Convert our internal component type to OpenStudio component type
    const osComponentType = componentTypeMap[componentType] || componentType;
    console.log(`Fetching properties for ${componentType} (OS type: ${osComponentType})`);

    const response = await api.get(`/idd?type=${osComponentType}`);
    console.log('API response:', response.data);

    const component = response.data.components[osComponentType];
    console.log('Component data:', component);

    // If we have reference lists, process object-list fields
    if (response.data.reference_lists && component) {
      // For each field in the component
      for (const [fieldName, field] of Object.entries(component.fields)) {
        // If the field has an object-list attribute
        if (field['object-list']) {
          const objectListName = field['object-list'];

          // If this object-list exists in our reference_lists
          if (response.data.reference_lists[objectListName]) {
            // Set the options to the components in the reference list
            field.options = response.data.reference_lists[objectListName];
          }
        }
      }
    }

    return component || null;
  } catch (error) {
    console.error(`Error fetching properties for ${componentType}:`, error);
    throw error;
  }
};

/**
 * Get default properties for a component type
 * @param {string} componentType - The component type
 * @returns {Promise<Object>} - Default properties
 */
export const getDefaultProperties = async (componentType) => {
  try {
    // Try to get properties from the IDD file
    const component = await getComponentProperties(componentType);

    if (component) {
      // Convert IDD fields to properties
      const properties = {};

      for (const [fieldName, field] of Object.entries(component.fields)) {
        // Skip the Name field
        if (fieldName === 'Name') continue;

        // Use default value if available, otherwise use a reasonable default
        let value = field.default;

        // Convert 'autosize' to a reasonable default
        if (value === 'autosize') {
          if (field.name.includes('FlowRate')) {
            value = 1.0;
          } else if (field.name.includes('Capacity')) {
            value = 10000;
          } else {
            value = 0;
          }
        }

        // Convert string numbers to actual numbers
        if (field.type === 'real' || field.type === 'integer') {
          value = parseFloat(value) || 0;
        }

        // Use camelCase for property names
        const propName = fieldName.charAt(0).toLowerCase() + fieldName.slice(1);
        properties[propName] = value;
      }

      return properties;
    }

    // Fall back to hardcoded defaults if IDD parsing fails
    console.warn(`No IDD data found for ${componentType}, using hardcoded defaults`);
    return getFallbackProperties(componentType);
  } catch (error) {
    console.error(`Error getting default properties for ${componentType}:`, error);
    // Fall back to hardcoded defaults
    return getFallbackProperties(componentType);
  }
};

/**
 * Fallback function to get hardcoded default properties
 * @param {string} componentType - The component type
 * @returns {Object} - Default properties
 */
const getFallbackProperties = (componentType) => {
  // Add some reasonable defaults for common properties
  switch (componentType) {
    case 'airHandler':
      return {
        airflow: 2000, // CFM
        fanPower: 1.5, // HP
        coolingCapacity: 60000, // BTU/h
        heatingCapacity: 80000, // BTU/h
        efficiency: 0.7
      };
    case 'chiller':
      return {
        capacity: 120, // Tons
        cop: 3.2,
        type: 'water-cooled',
        minPartLoadRatio: 0.1,
        optimalPartLoadRatio: 0.8
      };
    case 'boiler':
      return {
        capacity: 500000, // BTU/h
        efficiency: 0.85,
        fuelType: 'natural-gas',
        maxTemperature: 180 // °F
      };
    case 'coolingTower':
      return {
        capacity: 150, // Tons
        fanPower: 7.5, // HP
        type: 'open-circuit',
        approach: 7 // °F
      };
    case 'pump':
      return {
        flowRate: 100, // GPM
        head: 60, // ft
        power: 2, // HP
        efficiency: 0.7
      };
    case 'vav':
      return {
        airflow: 800, // CFM
        minAirflow: 200, // CFM
        reheatCapacity: 20000, // BTU/h
        zoneTemperature: 72 // °F
      };
    case 'zone':
      return {
        area: 1000, // sq ft
        peakCoolingLoad: 24000, // BTU/h
        peakHeatingLoad: 18000, // BTU/h
        occupancy: 10 // people
      };
    case 'airLoopHVAC':
      return {
        designSupplyAirFlowRate: 2000, // CFM
        availabilitySchedule: 'Always On',
        nightCycleControlType: 'StayOff'
      };
    case 'plantLoop':
      return {
        fluidType: 'Water',
        maximumLoopTemperature: 180, // °F
        minimumLoopTemperature: 40, // °F
        maximumLoopFlowRate: 100, // GPM
        minimumLoopFlowRate: 0, // GPM
        loadDistributionScheme: 'SequentialLoad'
      };
    case 'connectorMixer':
      return {
        description: 'Plant Loop Mixer'
      };
    case 'zoneMixer':
      return {
        description: 'Air Loop Zone Mixer'
      };
    case 'supplySideMixer':
      return {
        description: 'Air Loop Supply Side Mixer'
      };
    case 'connectorSplitter':
      return {
        description: 'Plant Loop Splitter'
      };
    case 'zoneSplitter':
      return {
        description: 'Air Loop Zone Splitter'
      };
    case 'supplySideSplitter':
      return {
        description: 'Air Loop Supply Side Splitter'
      };
    case 'fanConstantVolume':
      return {
        fanEfficiency: 0.7,
        pressureRise: 500, // Pa
        motorEfficiency: 0.9,
        maximumFlowRate: 1.0 // m³/s
      };
    case 'fanVariableVolume':
      return {
        fanEfficiency: 0.7,
        pressureRise: 500, // Pa
        motorEfficiency: 0.9,
        maximumFlowRate: 1.0 // m³/s
      };
    case 'coilCoolingWater':
      return {
        designWaterFlowRate: 0.0015, // m³/s
        designAirFlowRate: 1.0, // m³/s
        designInletWaterTemperature: 7.0, // °C
        designInletAirTemperature: 25.0, // °C
        designOutletAirTemperature: 10.0 // °C
      };
    case 'coilHeatingWater':
      return {
        designWaterFlowRate: 0.0015, // m³/s
        designAirFlowRate: 1.0, // m³/s
        designInletWaterTemperature: 60.0, // °C
        designInletAirTemperature: 10.0, // °C
        designOutletAirTemperature: 40.0 // °C
      };
    case 'outdoorAirSystem':
      return {
        controllerName: 'OA Controller',
        minimumOutdoorAirFlowRate: 500, // CFM
        maximumOutdoorAirFlowRate: 2000 // CFM
      };
    default:
      return {};
  }
};

export default {
  getComponentTypes,
  getComponentProperties,
  getDefaultProperties,
  componentTypeMap,
  reverseComponentTypeMap
};
