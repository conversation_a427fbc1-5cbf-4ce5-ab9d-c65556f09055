.component-panel {
  background-color: #f5f5f5;
  border-right: 1px solid #e0e0e0;
  padding: 16px;
  width: 250px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
}

.component-tabs {
  display: flex;
  margin-bottom: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.tab-button {
  padding: 8px 16px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #666;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.tab-button.active {
  color: #1976d2;
  border-bottom-color: #1976d2;
}

.tab-button:hover {
  background-color: #f0f0f0;
}

.standard-components, .idd-components {
  flex: 1;
  overflow-y: auto;
}

.panel-title {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 16px;
  color: #333;
}

/* Hierarchical Component Groups */
.component-group {
  margin-bottom: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background-color: white;
  overflow: hidden;
}

.group-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: #f8f9fa;
  cursor: pointer;
  border-bottom: 1px solid #e0e0e0;
  transition: background-color 0.2s;
}

.group-header:hover {
  background-color: #e9ecef;
}

.group-toggle {
  margin-right: 8px;
  font-size: 12px;
  color: #666;
  transition: transform 0.2s;
  user-select: none;
}

.group-toggle.expanded {
  transform: rotate(90deg);
}

.group-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: #333;
}

.group-content {
  padding: 16px;
}

.component-subgroup {
  margin-bottom: 20px;
}

.component-subgroup:last-child {
  margin-bottom: 0;
}

.subgroup-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 12px;
  color: #495057;
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 4px;
  padding-left: 8px;
}

.component-category {
  margin-bottom: 24px;
}

.category-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 12px;
  color: #666;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 4px;
}

.component-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: grab;
  margin-bottom: 6px;
  margin-left: 8px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  transition: background-color 0.2s, box-shadow 0.2s, transform 0.1s;
}

.component-item:hover {
  background-color: #e9ecef;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.component-item:active {
  cursor: grabbing;
  transform: translateY(0);
}

.component-icon {
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.component-label {
  font-size: 14px;
  color: #333;
}

/* IDD Component List Styles */
.idd-component-list {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.idd-component-list-header {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.idd-component-list-search {
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
}

.idd-component-list-group-select {
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
}

.idd-component-list-content {
  flex: 1;
  overflow-y: auto;
}

.idd-component-list-group {
  margin-bottom: 16px;
}

.idd-component-list-group-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #666;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 4px;
}

.idd-component-list-items {
  list-style: none;
  padding: 0;
  margin: 0;
}

.idd-component-list-item {
  padding: 8px 12px;
  border-radius: 4px;
  cursor: grab;
  margin-bottom: 4px;
  background-color: white;
  border: 1px solid #e0e0e0;
  transition: background-color 0.2s, box-shadow 0.2s;
  font-size: 14px;
  color: #333;
}

.idd-component-list-item:active {
  cursor: grabbing;
}

.idd-component-list-item-internal-type {
  display: block;
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

.idd-component-list-item:hover {
  background-color: #fafafa;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.idd-component-list-loading,
.idd-component-list-error,
.idd-component-list-empty {
  padding: 16px;
  text-align: center;
  color: #666;
  font-size: 14px;
}
