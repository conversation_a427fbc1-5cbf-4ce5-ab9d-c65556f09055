import React, { useState, useRef } from 'react';
import { useHVACStore } from '../store/hvacStore';

const ToolbarPanel = () => {
  const [showResults, setShowResults] = useState(false);
  const [results, setResults] = useState(null);
  const [systemName, setSystemName] = useState('');
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [saveStatus, setSaveStatus] = useState(null);
  const [loadStatus, setLoadStatus] = useState(null);
  const [importStatus, setImportStatus] = useState(null);
  const [exportStatus, setExportStatus] = useState(null);
  const fileInputRef = useRef(null);

  const calculateSystemPerformance = useHVACStore((state) => state.calculateSystemPerformance);
  const saveSystem = useHVACStore((state) => state.saveSystem);
  const loadSystem = useHVACStore((state) => state.loadSystem);
  const clearSystem = useHVACStore((state) => state.clearSystem);
  const exportSystemAsJSON = useHVACStore((state) => state.exportSystemAsJSON);
  const importSystemFromJSON = useHVACStore((state) => state.importSystemFromJSON);

  const handleCalculate = async () => {
    const calculationResults = await calculateSystemPerformance();
    setResults(calculationResults);
    setShowResults(true);
  };

  const handleSave = async () => {
    if (!systemName.trim()) {
      setSaveStatus({ success: false, message: 'Please enter a system name' });
      return;
    }

    const result = await saveSystem(systemName);

    if (result.success) {
      setSaveStatus({ success: true, message: 'System saved successfully' });
      setShowSaveDialog(false);
      setSystemName('');
    } else {
      setSaveStatus({ success: false, message: result.error || 'Failed to save system' });
    }

    setTimeout(() => {
      setSaveStatus(null);
    }, 3000);
  };

  const handleLoad = async () => {
    const result = await loadSystem();

    if (result.success) {
      setLoadStatus({ success: true, message: 'System loaded successfully' });
    } else {
      setLoadStatus({ success: false, message: result.error || 'Failed to load system' });
    }

    setTimeout(() => {
      setLoadStatus(null);
    }, 3000);
  };

  const handleClear = () => {
    if (window.confirm('Are you sure you want to clear the current system? This action cannot be undone.')) {
      clearSystem();
    }
  };

  const handleExportJSON = () => {
    const result = exportSystemAsJSON();

    if (result.success) {
      setExportStatus({ success: true, message: 'System exported successfully' });
    } else {
      setExportStatus({ success: false, message: result.error || 'Failed to export system' });
    }

    setTimeout(() => {
      setExportStatus(null);
    }, 3000);
  };

  const handleImportJSON = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    try {
      const result = await importSystemFromJSON(file);

      if (result.success) {
        setImportStatus({ success: true, message: 'System imported successfully' });
      } else {
        setImportStatus({ success: false, message: result.error || 'Failed to import system' });
      }
    } catch (error) {
      setImportStatus({ success: false, message: error.message || 'Failed to import system' });
    }

    // Reset the file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }

    setTimeout(() => {
      setImportStatus(null);
    }, 3000);
  };

  return (
    <div className="toolbar-panel">
      <div className="toolbar-actions">
        <button
          className="btn btn-primary"
          onClick={handleCalculate}
        >
          Calculate Performance
        </button>

        <button
          className="btn btn-secondary"
          onClick={() => setShowSaveDialog(true)}
        >
          Save System
        </button>

        <button
          className="btn btn-secondary"
          onClick={handleLoad}
        >
          Load System
        </button>

        <button
          className="btn btn-danger"
          onClick={handleClear}
        >
          Clear System
        </button>

        <div className="divider"></div>

        <button
          className="btn btn-secondary"
          onClick={handleExportJSON}
        >
          Export JSON
        </button>

        <button
          className="btn btn-secondary"
          onClick={() => fileInputRef.current?.click()}
        >
          Import JSON
        </button>
        <input
          type="file"
          ref={fileInputRef}
          style={{ display: 'none' }}
          accept=".json"
          onChange={handleImportJSON}
        />
      </div>

      {saveStatus && (
        <div className={`status-message ${saveStatus.success ? 'success' : 'error'}`}>
          {saveStatus.message}
        </div>
      )}

      {loadStatus && (
        <div className={`status-message ${loadStatus.success ? 'success' : 'error'}`}>
          {loadStatus.message}
        </div>
      )}

      {exportStatus && (
        <div className={`status-message ${exportStatus.success ? 'success' : 'error'}`}>
          {exportStatus.message}
        </div>
      )}

      {importStatus && (
        <div className={`status-message ${importStatus.success ? 'success' : 'error'}`}>
          {importStatus.message}
        </div>
      )}

      {showSaveDialog && (
        <div className="save-dialog">
          <h4>Save System</h4>
          <div className="property-field">
            <label>System Name</label>
            <input
              type="text"
              value={systemName}
              onChange={(e) => setSystemName(e.target.value)}
              placeholder="Enter a name for your system"
            />
          </div>
          <div className="dialog-actions">
            <button
              className="btn btn-secondary"
              onClick={() => setShowSaveDialog(false)}
            >
              Cancel
            </button>
            <button
              className="btn btn-primary"
              onClick={handleSave}
            >
              Save
            </button>
          </div>
        </div>
      )}

      {showResults && results && (
        <div className="results-panel">
          <div className="results-header">
            <h4>System Performance Results</h4>
            <button
              className="close-button"
              onClick={() => setShowResults(false)}
            >
              ×
            </button>
          </div>

          <div className="results-section">
            <h5>Loads</h5>
            <div className="result-item">
              <span className="result-label">Total Cooling Load:</span>
              <span className="result-value">{(results.loads.total_cooling_load / 12000).toFixed(1)} Tons</span>
            </div>
            <div className="result-item">
              <span className="result-label">Total Heating Load:</span>
              <span className="result-value">{(results.loads.total_heating_load / 1000).toFixed(1)} MBH</span>
            </div>
          </div>

          <div className="results-section">
            <h5>Capacities</h5>
            <div className="result-item">
              <span className="result-label">Cooling Capacity:</span>
              <span className="result-value">{(results.capacities.cooling_capacity / 12000).toFixed(1)} Tons</span>
            </div>
            <div className="result-item">
              <span className="result-label">Heating Capacity:</span>
              <span className="result-value">{(results.capacities.heating_capacity / 1000).toFixed(1)} MBH</span>
            </div>
            <div className="result-item">
              <span className="result-label">Cooling Capacity Ratio:</span>
              <span className={`result-value ${results.capacities.cooling_capacity_ratio < 1 ? 'warning' : ''}`}>
                {results.capacities.cooling_capacity_ratio.toFixed(2)}
              </span>
            </div>
            <div className="result-item">
              <span className="result-label">Heating Capacity Ratio:</span>
              <span className={`result-value ${results.capacities.heating_capacity_ratio < 1 ? 'warning' : ''}`}>
                {results.capacities.heating_capacity_ratio.toFixed(2)}
              </span>
            </div>
          </div>

          <div className="results-section">
            <h5>Energy Consumption</h5>
            <div className="result-item">
              <span className="result-label">Annual Cooling Energy:</span>
              <span className="result-value">{results.energy.annual_cooling_energy.toFixed(0)} kWh</span>
            </div>
            <div className="result-item">
              <span className="result-label">Annual Heating Energy:</span>
              <span className="result-value">{results.energy.annual_heating_energy.toFixed(0)} kWh</span>
            </div>
            <div className="result-item">
              <span className="result-label">Annual Fan Energy:</span>
              <span className="result-value">{results.energy.annual_fan_energy.toFixed(0)} kWh</span>
            </div>
            <div className="result-item">
              <span className="result-label">Annual Pump Energy:</span>
              <span className="result-value">{results.energy.annual_pump_energy.toFixed(0)} kWh</span>
            </div>
            <div className="result-item total">
              <span className="result-label">Total Annual Energy:</span>
              <span className="result-value">{results.energy.total_annual_energy.toFixed(0)} kWh</span>
            </div>
          </div>

          <div className="results-section">
            <h5>Efficiency</h5>
            <div className="result-item">
              <span className="result-label">System COP (Cooling):</span>
              <span className="result-value">{results.efficiency.cop_cooling.toFixed(2)}</span>
            </div>
            <div className="result-item">
              <span className="result-label">System COP (Heating):</span>
              <span className="result-value">{results.efficiency.cop_heating.toFixed(2)}</span>
            </div>
          </div>

          <div className="results-section">
            <h5>Cost</h5>
            <div className="result-item">
              <span className="result-label">Annual Energy Cost:</span>
              <span className="result-value">${results.cost.annual_energy_cost.toFixed(2)}</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ToolbarPanel;
