import React from 'react';
import { Position } from 'reactflow';

// PlantLoop Node Configuration
export const plantLoopConfig = {
  componentType: 'OS:PlantLoop',
  nodeType: 'plant-loop',
  // A PlantLoop has both supply and demand sides
  supplyComponents: [], // Will be populated when components are added
  demandComponents: [], // Will be populated when components are added
  // This is a container component that can have other components added to it
  isContainer: true,
  allowedComponents: [
    'OS:Pump:VariableSpeed',
    'OS:Pump:ConstantSpeed',
    'OS:Chiller:Electric:EIR',
    'OS:Boiler:HotWater',
    'OS:CoolingTower:SingleSpeed',
    'OS:Connector:Mixer',
    'OS:Connector:Splitter'
  ],
  inputs: [
    {
      id: 'supply-inlet',
      position: Position.Left,
      className: 'water-handle',
      style: { top: '30%' },
      side: 'supply'
    },
    {
      id: 'demand-outlet',
      position: Position.Left,
      className: 'water-handle',
      style: { top: '70%' },
      side: 'demand'
    }
  ],
  outputs: [
    {
      id: 'supply-outlet',
      position: Position.Right,
      className: 'water-handle',
      style: { top: '30%' },
      side: 'supply'
    },
    {
      id: 'demand-inlet',
      position: Position.Right,
      className: 'water-handle',
      style: { top: '70%' },
      side: 'demand'
    }
  ],
  displayProperties: [
    {
      name: 'fluidType',
      label: 'Fluid Type',
      unit: ''
    },
    {
      name: 'maximumLoopFlowRate',
      label: 'Max Flow',
      unit: 'GPM',
      format: (value) => value
    },
    {
      name: 'maximumLoopTemperature',
      label: 'Max Temp',
      unit: '°F',
      format: (value) => value
    },
    {
      name: 'minimumLoopTemperature',
      label: 'Min Temp',
      unit: '°F',
      format: (value) => value
    },
    {
      name: 'loadDistributionScheme',
      label: 'Load Scheme',
      unit: ''
    }
  ],
  icon: (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x="2" y="4" width="20" height="16" rx="2" stroke="#81c784" strokeWidth="2"/>
      <circle cx="12" cy="12" r="5" stroke="#81c784" strokeWidth="2"/>
      <path d="M7 12H17" stroke="#81c784" strokeWidth="2"/>
    </svg>
  )
};

export default plantLoopConfig;
