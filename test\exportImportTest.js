// Test script for export/import functionality
const fs = require('fs');
const path = require('path');

// Mock system data
const mockSystem = {
  nodes: [
    {
      id: '1',
      type: 'airHandler',
      position: { x: 100, y: 200 },
      data: {
        label: 'Air Handler 1',
        properties: {
          airflow: 2000,
          fanPower: 5,
          coolingCapacity: 10,
          heatingCapacity: 15
        }
      }
    },
    {
      id: '2',
      type: 'zone',
      position: { x: 300, y: 200 },
      data: {
        label: 'Zone 1',
        properties: {
          area: 1000,
          peakCoolingLoad: 5,
          peakHeatingLoad: 8,
          occupancy: 10
        }
      }
    }
  ],
  edges: [
    {
      id: 'e1-2',
      source: '1',
      target: '2',
      type: 'airFlow',
      data: { label: 'airFlow' }
    }
  ],
  timestamp: new Date().toISOString()
};

// Test export functionality
function testExport() {
  console.log('Testing export functionality...');
  
  // Create a test file
  const testFilePath = path.join(__dirname, 'test-system.json');
  fs.writeFileSync(testFilePath, JSON.stringify(mockSystem, null, 2));
  
  console.log(`Test file created at: ${testFilePath}`);
  console.log('Export test passed!');
  
  return testFilePath;
}

// Test import functionality
function testImport(filePath) {
  console.log('\nTesting import functionality...');
  
  // Read the test file
  const fileContent = fs.readFileSync(filePath, 'utf8');
  const importedSystem = JSON.parse(fileContent);
  
  // Validate the imported data
  if (!importedSystem.nodes || !Array.isArray(importedSystem.nodes)) {
    console.error('Import test failed: Invalid nodes data');
    return false;
  }
  
  if (!importedSystem.edges || !Array.isArray(importedSystem.edges)) {
    console.error('Import test failed: Invalid edges data');
    return false;
  }
  
  console.log('Imported system:');
  console.log(`- Nodes: ${importedSystem.nodes.length}`);
  console.log(`- Edges: ${importedSystem.edges.length}`);
  console.log('Import test passed!');
  
  // Clean up
  fs.unlinkSync(filePath);
  console.log(`Test file removed: ${filePath}`);
  
  return true;
}

// Run tests
try {
  const testFilePath = testExport();
  testImport(testFilePath);
  console.log('\nAll tests passed!');
} catch (error) {
  console.error('Test failed:', error);
}
