import { calculateEnergyAPI } from './api';

/**
 * Calculate energy performance for an HVAC system
 * @param {Object} system - The system to calculate
 * @returns {Promise<Object>} - The calculation results
 */
export const calculateEnergy = async (system) => {
  try {
    // In a production environment, this would call the API
    // For now, we'll use a mock implementation

    // Uncomment this to use the real API
    // const response = await calculateEnergyAPI(system);
    // return response.results;

    // Mock implementation
    return mockCalculateEnergy(system);
  } catch (error) {
    console.error('Error calculating energy:', error);
    throw error;
  }
};

/**
 * Mock implementation of energy calculation
 * @param {Object} system - The system to calculate
 * @returns {Object} - The calculation results
 */
function mockCalculateEnergy(system) {
  // Get all zones
  const zones = system.nodes.filter(node => node.type === 'zone');

  // Calculate total loads
  const totalCoolingLoad = zones.reduce(
    (sum, zone) => sum + zone.data.properties.peakCoolingLoad,
    0
  );

  const totalHeatingLoad = zones.reduce(
    (sum, zone) => sum + zone.data.properties.peakHeatingLoad,
    0
  );

  // Get all equipment
  const chillers = system.nodes.filter(node => node.type === 'chiller');
  const boilers = system.nodes.filter(node => node.type === 'boiler');
  const airHandlers = system.nodes.filter(node => node.type === 'airHandler');
  const pumps = system.nodes.filter(node => node.type === 'pump');

  // Calculate capacities
  const coolingCapacity = chillers.reduce(
    (sum, chiller) => sum + chiller.data.properties.capacity * 12000, // Convert tons to BTU/h
    0
  );

  const heatingCapacity = boilers.reduce(
    (sum, boiler) => sum + boiler.data.properties.capacity,
    0
  );

  // Calculate energy consumption (simplified)
  const annualCoolingEnergy = totalCoolingLoad * 1200 * 0.000293071 / 3.0; // kWh
  const annualHeatingEnergy = totalHeatingLoad * 800 * 0.000293071 / 0.85; // kWh

  const annualFanEnergy = airHandlers.reduce(
    (sum, ahu) => sum + ahu.data.properties.fanPower * 0.7457 * 3000, // kWh
    0
  );

  const annualPumpEnergy = pumps.reduce(
    (sum, pump) => sum + pump.data.properties.power * 0.7457 * 3000, // kWh
    0
  );

  const totalAnnualEnergy = annualCoolingEnergy + annualHeatingEnergy + annualFanEnergy + annualPumpEnergy;

  // Calculate efficiency metrics
  const copCooling = chillers.length > 0
    ? chillers.reduce((sum, chiller) => sum + chiller.data.properties.cop, 0) / chillers.length * 0.85
    : 0;

  const copHeating = boilers.length > 0
    ? boilers.reduce((sum, boiler) => sum + boiler.data.properties.efficiency, 0) / boilers.length
    : 0;

  // Calculate energy cost
  const electricityCost = 0.12; // $/kWh
  const annualEnergyCost = totalAnnualEnergy * electricityCost;

  return {
    loads: {
      total_cooling_load: totalCoolingLoad,
      total_heating_load: totalHeatingLoad
    },
    capacities: {
      cooling_capacity: coolingCapacity,
      heating_capacity: heatingCapacity,
      cooling_capacity_ratio: totalCoolingLoad > 0 ? coolingCapacity / totalCoolingLoad : 0,
      heating_capacity_ratio: totalHeatingLoad > 0 ? heatingCapacity / totalHeatingLoad : 0
    },
    energy: {
      annual_cooling_energy: annualCoolingEnergy,
      annual_heating_energy: annualHeatingEnergy,
      annual_fan_energy: annualFanEnergy,
      annual_pump_energy: annualPumpEnergy,
      total_annual_energy: totalAnnualEnergy
    },
    efficiency: {
      cop_cooling: copCooling,
      cop_heating: copHeating
    },
    cost: {
      annual_energy_cost: annualEnergyCost
    }
  };
}
