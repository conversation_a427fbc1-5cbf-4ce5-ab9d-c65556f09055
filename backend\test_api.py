import requests
import json

def test_api():
    """Test the FastAPI endpoints"""
    base_url = "http://localhost:8000"
    
    # Test root endpoint
    print("Testing root endpoint...")
    response = requests.get(f"{base_url}/")
    print(f"Status code: {response.status_code}")
    print(f"Response: {response.json()}")
    print()
    
    # Test components endpoint
    print("Testing components endpoint...")
    response = requests.get(f"{base_url}/idd/components")
    print(f"Status code: {response.status_code}")
    components = response.json().get('components', [])
    print(f"Found {len(components)} components")
    
    # Print first 5 components
    for i, component in enumerate(components[:5]):
        print(f"{i+1}. {component['name']} ({component['group']})")
    print()
    
    # Test IDD endpoint with a specific component
    if components:
        component_name = components[0]['name']
        print(f"Testing IDD endpoint with component: {component_name}")
        response = requests.get(f"{base_url}/idd?type={component_name}")
        print(f"Status code: {response.status_code}")
        component_data = response.json().get('components', {}).get(component_name, {})
        
        # Print component fields
        fields = component_data.get('fields', {})
        print(f"Component has {len(fields)} fields:")
        for i, (field_name, field_data) in enumerate(list(fields.items())[:5]):
            print(f"{i+1}. {field_name}: {field_data.get('type')} (required: {field_data.get('required')})")

if __name__ == "__main__":
    test_api()
