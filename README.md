# HVAC Designer

A React Next.js application for designing HVAC systems, inspired by OpenStudio's HVAC module implementation.

## Features

- Interactive HVAC system design using ReactFlow
- Component library with common HVAC elements
- Property editing for components
- IDD file parsing for component definitions
- System saving and loading

## Project Structure

```bash
hvac-designer/
├── backend/                  # FastAPI backend
│   ├── data/                 # Data files
│   │   └── OpenStudio.idd    # OpenStudio IDD file
│   ├── main.py               # FastAPI application
│   ├── run.py                # Script to run the backend
│   └── requirements.txt      # Python dependencies
├── components/               # React components
│   ├── HVACDesigner.jsx      # Main designer component
│   ├── ComponentPanel.jsx    # Component panel
│   ├── PropertiesPanel.jsx   # Properties panel
│   ├── IDDComponentList.jsx  # IDD component list
│   └── nodes/                # Node components
├── services/                 # API services
│   ├── api.js                # API client
│   ├── iddService.js         # IDD service
│   └── energyCalculation.js  # Energy calculation service
├── store/                    # State management
│   └── hvacStore.js          # HVAC store
├── utils/                    # Utility functions
│   └── iddParser.js          # IDD parser
└── pages/                    # Next.js pages
    └── index.js              # Main page
```

## Prerequisites

- Node.js (v14 or later)
- Python (v3.8 or later)

## Installation

1. Clone the repository:

```bash
git clone https://github.com/yourusername/hvac-designer.git
cd hvac-designer
```

2. Install frontend dependencies:

```bash
npm install
```

3. Install backend dependencies:

```bash
cd backend
pip install -r requirements.txt
cd ..
```

## Running the Application

### Running Frontend and Backend Separately

1. Start the backend:

```bash
npm run dev:backend
```

2. In a separate terminal, start the frontend:

```bash
npm run dev
```

3. Open [http://localhost:3000](http://localhost:3000) in your browser.

### Running Frontend and Backend Together

```bash
npm run dev:all
```

## How It Works

1. The backend parses the OpenStudio IDD file and exposes the component definitions through a REST API.
2. The frontend fetches the component definitions from the backend and displays them in the component panel.
3. Users can drag and drop components from the panel to the canvas to create HVAC systems.
4. Component properties are defined based on the IDD file definitions.

## API Endpoints

- `GET /` - Root endpoint to check if API is running
- `GET /idd` - Get IDD data, optionally filtered by component type
- `GET /idd/components` - List all available component types
