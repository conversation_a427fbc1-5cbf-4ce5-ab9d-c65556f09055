import React, { memo } from 'react';
import { <PERSON><PERSON>, Position } from 'reactflow';

const ZoneNode = ({ data, selected }) => {
  const { label, properties } = data;
  
  return (
    <div className={`zone-node ${selected ? 'selected' : ''}`}>
      {/* Input handles */}
      <Handle
        type="target"
        position={Position.Left}
        id="supply-air-in"
        className="handle air-handle"
        style={{ top: '50%' }}
      />
      
      <div className="node-content">
        <div className="node-header">{label}</div>
        <div className="node-body">
          <div className="node-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect x="3" y="3" width="18" height="18" rx="2" stroke="#a1887f" strokeWidth="2"/>
              <path d="M7 7H17" stroke="#a1887f" strokeWidth="2"/>
              <path d="M7 12H17" stroke="#a1887f" strokeWidth="2"/>
              <path d="M7 17H17" stroke="#a1887f" strokeWidth="2"/>
            </svg>
          </div>
          <div className="node-details">
            <div className="property-row">
              <span className="property-label">Area:</span>
              <span className="property-value">{properties.area} ft²</span>
            </div>
            <div className="property-row">
              <span className="property-label">Cooling Load:</span>
              <span className="property-value">{properties.peakCoolingLoad / 1000} MBH</span>
            </div>
          </div>
        </div>
      </div>
      
      {/* Output handles */}
      <Handle
        type="source"
        position={Position.Right}
        id="return-air-out"
        className="handle air-handle"
        style={{ top: '50%' }}
      />
    </div>
  );
};

export default memo(ZoneNode);

