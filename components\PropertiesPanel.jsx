import React, { useState, useEffect } from 'react';
import { useHVACStore } from '../store/hvacStore';
import { generatePropertyFields } from '../utils/propertiesGenerator';
import TabbedPropertyFields from './TabbedPropertyFields';

const PropertiesPanel = ({ node }) => {
  const updateNodeProperties = useHVACStore((state) => state.updateNodeProperties);
  const removeNode = useHVACStore((state) => state.removeNode);
  const saveSystem = useHVACStore((state) => state.saveSystem);
  const [iddFields, setIddFields] = useState(null);
  const [saveStatus, setSaveStatus] = useState(null);

  // State for panel width
  const [panelWidth, setPanelWidth] = useState(300); // Default width
  const [startX, setStartX] = useState(null);
  const [startWidth, setStartWidth] = useState(null);

  // Load saved width from localStorage if available
  useEffect(() => {
    const savedWidth = localStorage.getItem('propertiesPanelWidth');
    if (savedWidth) {
      setPanelWidth(parseInt(savedWidth, 10));
    }
  }, []);

  // Mouse down handler for resize
  const handleMouseDown = (e) => {
    e.preventDefault();
    setStartX(e.clientX);
    setStartWidth(panelWidth);

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    document.body.style.cursor = 'col-resize';
  };

  // Mouse move handler for resize
  const handleMouseMove = (e) => {
    if (startX === null) return;

    const delta = startX - e.clientX;
    const newWidth = Math.max(200, Math.min(window.innerWidth / 2, startWidth + delta));

    setPanelWidth(newWidth);
  };

  // Mouse up handler for resize
  const handleMouseUp = () => {
    setStartX(null);
    setStartWidth(null);

    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
    document.body.style.cursor = '';

    // Save width to localStorage
    localStorage.setItem('propertiesPanelWidth', panelWidth.toString());
  };

  // Clean up event listeners on unmount
  useEffect(() => {
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, []);

  // Extract node data if available
  const id = node?.id;
  const type = node?.type;
  const label = node?.data?.label;
  const properties = node?.data?.properties || {};

  // We no longer need to fetch component fields here
  // The IDDPropertyFields component will handle that

  const handlePropertyChange = (property, value) => {
    // Convert string values to numbers for numeric properties
    const numericProperties = [
      'airflow', 'fanPower', 'coolingCapacity', 'heatingCapacity', 'efficiency',
      'capacity', 'cop', 'minPartLoadRatio', 'optimalPartLoadRatio',
      'maxTemperature', 'approach', 'flowRate', 'head', 'power',
      'minAirflow', 'reheatCapacity', 'zoneTemperature',
      'area', 'peakCoolingLoad', 'peakHeatingLoad', 'occupancy'
    ];

    if (typeof value === 'string' && numericProperties.includes(property)) {
      value = parseFloat(value) || 0;
    }

    updateNodeProperties(id, { [property]: value });
  };

  const handleLabelChange = (e) => {
    const newLabel = e.target.value;
    updateNodeProperties(id, { label: newLabel });
  };

  const handleRemoveNode = () => {
    removeNode(id);
  };

  const handleSaveProperties = async () => {
    // Save the entire system to persist the properties
    const result = await saveSystem(`${type} ${label}`);

    if (result.success) {
      setSaveStatus({ success: true, message: 'Properties saved successfully' });
    } else {
      setSaveStatus({ success: false, message: result.error || 'Failed to save properties' });
    }

    setTimeout(() => {
      setSaveStatus(null);
    }, 3000);
  };

  const renderPropertyFields = () => {
    if (!type || !properties) {
      return null;
    }

    // First try to use the TabbedPropertyFields component
    return (
      <>
        <TabbedPropertyFields
          componentType={type}
          properties={properties}
          onPropertyChange={handlePropertyChange}
          onFieldsLoaded={setIddFields}
        />

        {/* Only use fallback if TabbedPropertyFields doesn't render anything */}
        {!iddFields && generatePropertyFields(type, properties, handlePropertyChange)}
      </>
    );
  };



  // If no node is selected, show a message
  if (!node) {
    return (
      <div className="properties-panel-container">
        <div
          className="properties-panel"
          style={{ width: `${panelWidth}px` }}
        >
          <div
            className={`resize-handle ${startX !== null ? 'active' : ''}`}
            onMouseDown={handleMouseDown}
          />
          <h3 className="panel-title">Properties</h3>
          <div className="no-selection-message">
            Select a component to view and edit its properties
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="properties-panel-container">
      <div
        className="properties-panel"
        style={{ width: `${panelWidth}px` }}
      >
        <div
          className={`resize-handle ${startX !== null ? 'active' : ''}`}
          onMouseDown={handleMouseDown}
        />
        <h3 className="panel-title">Properties</h3>

        <div className="component-type">
          {type.startsWith('OS:')
            ? type.split(':').slice(1).join(':').replace(/([A-Z])/g, ' $1').trim()
            : type.charAt(0).toUpperCase() + type.slice(1)}
        </div>

        <div className="property-field">
          <label>Label</label>
          <input
            type="text"
            value={label || ''}
            onChange={handleLabelChange}
          />
        </div>

        {renderPropertyFields()}

        {saveStatus && (
          <div className={`status-message ${saveStatus.success ? 'success' : 'error'}`}>
            {saveStatus.message}
          </div>
        )}

        <div className="panel-actions">
          <button
            className="btn btn-primary"
            onClick={handleSaveProperties}
          >
            Save Properties
          </button>
          <button
            className="btn btn-danger"
            onClick={handleRemoveNode}
          >
            Remove Component
          </button>
        </div>
      </div>
    </div>
  );
};

export default PropertiesPanel;
