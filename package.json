{"name": "hvac-designer", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "backend": "cd backend && python run.py", "dev:backend": "cd backend && python -m uvicorn main:app --reload --port 8000", "dev:all": "concurrently \"npm run dev\" \"npm run dev:backend\""}, "dependencies": {"axios": "^1.4.0", "bootstrap": "^5.3.5", "next": "^13.4.0", "react": "^18.2.0", "react-bootstrap": "^2.10.9", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "reactflow": "^11.7.0", "uuid": "^9.0.0", "zustand": "^4.3.8"}, "devDependencies": {"concurrently": "^8.0.1", "eslint": "^8.40.0", "eslint-config-next": "^13.4.0"}}