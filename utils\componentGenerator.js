import React from 'react';
import { <PERSON><PERSON>, Position } from 'reactflow';
import { getComponentMetadata } from './iddParser';

/**
 * Generate a React component for an OpenStudio component
 * @param {string} componentType - The OpenStudio component type
 * @param {string} nodeType - The node type in our application
 * @param {Object} handleConfig - Configuration for the handles
 * @returns {Function} - The React component
 */
export const generateNodeComponent = (componentType, nodeType, handleConfig) => {
  // Get component metadata
  const componentDef = getComponentMetadata(componentType);

  // Create the React component
  const NodeComponent = ({ data, selected }) => {
    const { label, properties } = data;

    // Get the display properties (the ones to show in the node)
    const displayProps = handleConfig.displayProperties || [];

    return (
      <div className={`${nodeType}-node ${selected ? 'selected' : ''}`}>
        {/* Input handles */}
        {handleConfig.inputs.map((input, index) => (
          <Handle
            key={`input-${index}`}
            type="target"
            position={input.position}
            id={input.id}
            className={`handle ${input.className}`}
            style={input.style}
          />
        ))}

        <div className="node-content">
          <div className="node-header">{label}</div>
          <div className="node-body">
            <div className="node-icon">
              {handleConfig.icon}
            </div>
            <div className="node-details">
              {displayProps.map((prop, index) => (
                <div key={`prop-${index}`} className="property-row">
                  <span className="property-label">{prop.label}:</span>
                  <span className="property-value">
                    {prop.format ? prop.format(properties[prop.name]) : properties[prop.name]} {prop.unit}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Output handles */}
        {handleConfig.outputs.map((output, index) => (
          <Handle
            key={`output-${index}`}
            type="source"
            position={output.position}
            id={output.id}
            className={`handle ${output.className}`}
            style={output.style}
          />
        ))}
      </div>
    );
  };

  return React.memo(NodeComponent);
};

export default {
  generateNodeComponent
};
