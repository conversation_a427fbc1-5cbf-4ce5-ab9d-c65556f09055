import React, { useState, useEffect } from 'react';
import iddService from '../services/iddService';
import 'bootstrap/dist/css/bootstrap.min.css';

/**
 * Render an input field based on the field type
 * @param {Object} field - The field definition from the IDD file
 * @param {string} propName - The property name
 * @param {string} fieldName - The original field name
 * @param {any} value - The current value
 * @param {Function} handlePropertyChange - Function to handle property changes
 * @param {Function} onSelectReference - Function to handle selecting a reference
 * @returns {JSX.Element} - The input field
 */
const renderFieldInput = (field, propName, fieldName, value, handlePropertyChange, onSelectReference) => {
  // Handle different field types
  switch (field.type) {
    case 'choice':
      return (
        <select
          value={value || field.default || ''}
          onChange={(e) => handlePropertyChange(propName, e.target.value)}
        >
          {field.options.map(option => (
            <option key={option} value={option}>{option}</option>
          ))}
        </select>
      );

    case 'real':
      return (
        <input
          type="number"
          step="0.01"
          value={value !== undefined ? value : ''}
          onChange={(e) => handlePropertyChange(propName, e.target.value)}
        />
      );

    case 'integer':
      return (
        <input
          type="number"
          step="1"
          value={value !== undefined ? value : ''}
          onChange={(e) => handlePropertyChange(propName, e.target.value)}
        />
      );

    case 'alpha':
    case 'string':
      return (
        <input
          type="text"
          value={value || ''}
          onChange={(e) => handlePropertyChange(propName, e.target.value)}
        />
      );

    case 'object-list':
      // If we have options (from reference lists), use a select that automatically creates tabs
      if (field.options && field.options.length > 0) {
        return (
          <div className="object-list-field">
            <select
              value={value || ''}
              onChange={(e) => {
                const selectedValue = e.target.value;
                // Update the property value
                handlePropertyChange(propName, selectedValue);

                // If a value is selected, fetch and show its properties in a tab
                if (selectedValue && onSelectReference) {
                  console.log(`Selected ${selectedValue} for field ${fieldName}`);
                  onSelectReference(selectedValue, fieldName);
                }
              }}
            >
              <option value="">-- Select {field['object-list']} --</option>
              {field.options.map(option => (
                <option key={option} value={option}>{option}</option>
              ))}
            </select>
          </div>
        );
      }
      // Otherwise, use a text input
      return (
        <input
          type="text"
          value={value || ''}
          onChange={(e) => handlePropertyChange(propName, e.target.value)}
        />
      );

    default:
      return (
        <input
          type="text"
          value={value || ''}
          onChange={(e) => handlePropertyChange(propName, e.target.value)}
        />
      );
  }
};

/**
 * Component to display property fields from the IDD file
 * @param {Object} props - Component props
 * @param {string} props.componentType - The component type
 * @param {Object} props.properties - The current property values
 * @param {Function} props.onPropertyChange - Function to handle property changes
 * @param {Function} props.onFieldsLoaded - Function called when fields are loaded
 * @returns {JSX.Element} - Rendered component
 */
const IDDPropertyFields = ({ componentType, properties, onPropertyChange, onFieldsLoaded }) => {
  const [iddFields, setIddFields] = useState(null);
  const [loading, setLoading] = useState(true);

  // State for managing tabs
  const [activeTab, setActiveTab] = useState('main');
  const [tabs, setTabs] = useState([{ id: 'main', title: componentType, type: componentType }]);

  // State for referenced components
  const [referencedComponents, setReferencedComponents] = useState({});
  const [loadingReferences, setLoadingReferences] = useState({});

  // Fetch IDD fields asynchronously
  useEffect(() => {
    const fetchIddFields = async () => {
      try {
        // Reset tabs and referenced components when component type changes
        setActiveTab('main');
        setTabs([{ id: 'main', title: componentType, type: componentType }]);
        setReferencedComponents({});
        setLoadingReferences({});

        // Convert our internal component type to OpenStudio component type
        const osComponentType = iddService.componentTypeMap[componentType] || componentType;

        // Fetch component properties from the IDD file
        console.log(`Fetching IDD properties for ${osComponentType}`);
        const component = await iddService.getComponentProperties(osComponentType);
        console.log('Received component data:', component);

        if (component && component.fields) {
          console.log('Setting IDD fields:', component.fields);
          setIddFields(component.fields);

          // Call onFieldsLoaded if provided
          if (onFieldsLoaded) {
            onFieldsLoaded(component.fields);
          }
        } else {
          console.warn(`No fields found for ${osComponentType}`);

          // Call onFieldsLoaded with null if provided
          if (onFieldsLoaded) {
            onFieldsLoaded(null);
          }
        }

        setLoading(false);
      } catch (error) {
        console.error(`Error fetching IDD fields for ${componentType}:`, error);
        setLoading(false);

        // Call onFieldsLoaded with null if provided
        if (onFieldsLoaded) {
          onFieldsLoaded(null);
        }
      }
    };

    if (componentType) {
      fetchIddFields();
    }
  }, [componentType]);

  // Fetch referenced component properties
  const fetchReferencedComponent = async (componentType, fieldName) => {
    try {
      console.log(`Starting to fetch referenced component: ${componentType} for field: ${fieldName}`);

      // Create a unique ID for this reference
      const referenceId = `${fieldName}-${componentType}`;
      console.log(`Reference ID: ${referenceId}`);

      // Set loading state for this reference
      setLoadingReferences(prev => {
        console.log('Setting loading state:', { ...prev, [referenceId]: true });
        return { ...prev, [referenceId]: true };
      });

      console.log(`Fetching referenced component properties for ${componentType}`);
      const component = await iddService.getComponentProperties(componentType);
      console.log('Received referenced component data:', component);

      if (component && component.fields) {
        console.log(`Got fields for ${componentType}, updating state`);

        // Store the referenced component fields
        setReferencedComponents(prev => {
          const newState = {
            ...prev,
            [referenceId]: component.fields
          };
          console.log('New referencedComponents state:', newState);
          return newState;
        });

        // Add a new tab for this reference if it doesn't exist
        setTabs(prevTabs => {
          // Check if tab already exists
          const tabExists = prevTabs.some(tab => tab.id === referenceId);
          console.log(`Tab exists: ${tabExists}`);

          if (!tabExists) {
            const newTabs = [
              ...prevTabs,
              {
                id: referenceId,
                title: `${fieldName}: ${componentType}`,
                type: componentType,
                fieldName: fieldName
              }
            ];
            console.log('New tabs state:', newTabs);
            return newTabs;
          }
          return prevTabs;
        });

        // Switch to the new tab
        console.log(`Setting active tab to: ${referenceId}`);
        setActiveTab(referenceId);
      } else {
        console.warn(`No fields found for referenced component ${componentType}`);
      }

      // Clear loading state
      setLoadingReferences(prev => ({ ...prev, [referenceId]: false }));
    } catch (error) {
      console.error(`Error fetching referenced component fields for ${componentType}:`, error);

      // Clear loading state
      const referenceId = `${fieldName}-${componentType}`;
      setLoadingReferences(prev => ({ ...prev, [referenceId]: false }));
    }
  };

  // If we're still loading, show a loading message
  if (loading) {
    return <div className="loading-properties">Loading properties...</div>;
  }

  // Handle selecting a referenced component
  const handleSelectReference = (componentType, fieldName) => {
    console.log(`handleSelectReference called with componentType: ${componentType}, fieldName: ${fieldName}`);
    fetchReferencedComponent(componentType, fieldName);
  };

  // Render fields for a component
  const renderComponentFields = (fields, componentProps, isReference = false) => {
    return Object.entries(fields).map(([fieldName, field]) => {
      // Skip the Name field as it's handled separately (for main component only)
      if (!isReference && fieldName === 'Name') return null;

      // Convert field name to camelCase for property name
      const propName = fieldName.charAt(0).toLowerCase() + fieldName.slice(1);

      // Get the current property value or use the default
      const value = isReference
        ? field.default // For referenced components, use defaults
        : (componentProps[propName] !== undefined ? componentProps[propName] : field.default);

      // Generate the appropriate input field based on the field type
      return (
        <div className="property-field" key={fieldName}>
          <label>
            {fieldName}
            {field.required && <span className="required">*</span>}
            {field.units && <span className="units"> ({field.units})</span>}
            {field.note && <span className="note" title={field.note}>ℹ️</span>}
          </label>

          {renderFieldInput(
            field,
            propName,
            fieldName,
            value,
            isReference ? () => {} : onPropertyChange, // Only allow editing for main component
            isReference ? null : handleSelectReference // Only allow creating tabs from main component
          )}
        </div>
      );
    });
  };

  // If we have IDD fields, use them to generate property fields with tabs
  if (iddFields) {
    return (
      <div className="tabbed-properties">
        <Tabs
          activeKey={activeTab}
          onSelect={(key) => setActiveTab(key)}
          className="properties-tabs"
        >
          {/* Main component tab */}
          <Tab eventKey="main" title={componentType}>
            <div className="tab-content-container">
              {renderComponentFields(iddFields, properties)}
            </div>
          </Tab>

          {/* Referenced component tabs */}
          {tabs.filter(tab => tab.id !== 'main').map(tab => {
            const referenceId = tab.id;
            const referenceFields = referencedComponents[referenceId];
            const isLoading = loadingReferences[referenceId];

            return (
              <Tab eventKey={referenceId} title={tab.title} key={referenceId}>
                <div className="tab-content-container">
                  {isLoading ? (
                    <div className="loading-properties">Loading {tab.type} properties...</div>
                  ) : referenceFields ? (
                    renderComponentFields(referenceFields, {}, true)
                  ) : (
                    <div className="error-message">Failed to load {tab.type} properties</div>
                  )}
                </div>
              </Tab>
            );
          })}
        </Tabs>
      </div>
    );
  }

  // If we couldn't get IDD fields, return null
  return null;
};



export default IDDPropertyFields;
