/**
 * IDD parser that uses the iddService to fetch component properties from the backend
 * Falls back to hardcoded defaults if the backend is not available
 */
import iddService from '../services/iddService';

/**
 * Get default properties for a component type
 * @param {string} componentType - The component type
 * @returns {Object} - The default properties
 */
export const getDefaultProperties = async (componentType) => {
  try {
    // Try to get properties from the backend
    return await iddService.getDefaultProperties(componentType);
  } catch (error) {
    console.error(`Error getting default properties for ${componentType}:`, error);
    // Fall back to hardcoded defaults
    return getFallbackProperties(componentType);
  }
};

/**
 * Synchronous version of getDefaultProperties that returns hardcoded defaults
 * Use this when you need properties immediately and can't wait for an async call
 * @param {string} componentType - The component type
 * @returns {Object} - The default properties
 */
export const getDefaultPropertiesSync = (componentType) => {
  // Check if this is an OpenStudio component type (starts with OS:)
  const isOSComponent = componentType.startsWith('OS:');

  // If it's an OpenStudio component, try to map it to our internal type
  const internalType = isOSComponent ?
    (getComponentTypeMap()[componentType] || componentType) :
    componentType;

  console.log(`Getting default properties for ${componentType} (internal type: ${internalType})`);

  // Return fallback properties for the internal type
  return getFallbackProperties(internalType);
};

/**
 * Get component metadata for a component type
 * @param {string} componentType - The OpenStudio component type
 * @returns {Promise<Object>} - The component metadata
 */
export const getComponentMetadata = async (componentType) => {
  try {
    // Map OpenStudio component types to our simplified component types
    const type = getComponentTypeMap()[componentType] || componentType;

    // Get properties from the backend
    const properties = await getDefaultProperties(type);

    return {
      type,
      properties
    };
  } catch (error) {
    console.error(`Error getting metadata for ${componentType}:`, error);

    // Fall back to hardcoded defaults
    const type = getComponentTypeMap()[componentType] || componentType;
    return {
      type,
      properties: getFallbackProperties(type)
    };
  }
};

/**
 * Synchronous version of getComponentMetadata that returns hardcoded defaults
 * @param {string} componentType - The OpenStudio component type
 * @returns {Object} - The component metadata
 */
export const getComponentMetadataSync = (componentType) => {
  const type = getComponentTypeMap()[componentType] || componentType;
  return {
    type,
    properties: getFallbackProperties(type)
  };
};

/**
 * Get the mapping of OpenStudio component types to our simplified component types
 * @returns {Object} - The component type map
 */
export const getComponentTypeMap = () => {
  return {
    'OS:AirLoopHVAC': 'airLoopHVAC',
    'OS:AirLoopHVAC:UnitarySystem': 'airHandler',
    'OS:Chiller:Electric:EIR': 'chiller',
    'OS:Boiler:HotWater': 'boiler',
    'OS:CoolingTower:SingleSpeed': 'coolingTower',
    'OS:Pump:VariableSpeed': 'pump',
    'OS:AirTerminal:SingleDuct:VAV:Reheat': 'vav',
    'OS:ThermalZone': 'zone',
    'OS:PlantLoop': 'plantLoop',
    'OS:Connector:Mixer': 'connectorMixer',
    'OS:AirLoopHVAC:ZoneMixer': 'zoneMixer',
    'OS:Connector:Splitter': 'connectorSplitter',
    'OS:AirLoopHVAC:ZoneSplitter': 'zoneSplitter',
    'OS:AirLoopHVAC:OutdoorAirSystem': 'outdoorAirSystem',
    'OS:Fan:ConstantVolume': 'fanConstantVolume',
    'OS:Fan:VariableVolume': 'fanVariableVolume',
    'OS:Coil:Cooling:Water': 'coilCoolingWater',
    'OS:Coil:Heating:Water': 'coilHeatingWater'
  };
};

/**
 * Fallback function to get hardcoded default properties
 * @param {string} componentType - The component type
 * @returns {Object} - Default properties
 */
const getFallbackProperties = (componentType) => {
  // Add some reasonable defaults for common properties
  switch (componentType) {
    case 'airHandler':
      return {
        airflow: 2000, // CFM
        fanPower: 1.5, // HP
        coolingCapacity: 60000, // BTU/h
        heatingCapacity: 80000, // BTU/h
        efficiency: 0.7
      };
    case 'chiller':
      return {
        capacity: 120, // Tons
        cop: 3.2,
        type: 'water-cooled',
        minPartLoadRatio: 0.1,
        optimalPartLoadRatio: 0.8
      };
    case 'boiler':
      return {
        capacity: 500000, // BTU/h
        efficiency: 0.85,
        fuelType: 'natural-gas',
        maxTemperature: 180 // °F
      };
    case 'coolingTower':
      return {
        capacity: 150, // Tons
        fanPower: 7.5, // HP
        type: 'open-circuit',
        approach: 7 // °F
      };
    case 'pump':
      return {
        flowRate: 100, // GPM
        head: 60, // ft
        power: 2, // HP
        efficiency: 0.7
      };
    case 'vav':
      return {
        airflow: 800, // CFM
        minAirflow: 200, // CFM
        reheatCapacity: 20000, // BTU/h
        zoneTemperature: 72 // °F
      };
    case 'zone':
      return {
        area: 1000, // sq ft
        peakCoolingLoad: 24000, // BTU/h
        peakHeatingLoad: 18000, // BTU/h
        occupancy: 10 // people
      };
    case 'airLoopHVAC':
      return {
        designSupplyAirFlowRate: 2000, // CFM
        availabilitySchedule: 'Always On',
        nightCycleControlType: 'StayOff'
      };
    case 'plantLoop':
      return {
        fluidType: 'Water',
        maximumLoopTemperature: 180, // °F
        minimumLoopTemperature: 40, // °F
        maximumLoopFlowRate: 100, // GPM
        minimumLoopFlowRate: 0, // GPM
        loadDistributionScheme: 'SequentialLoad'
      };
    case 'connectorMixer':
      return {
        description: 'Plant Loop Mixer'
      };
    case 'zoneMixer':
      return {
        description: 'Air Loop Zone Mixer'
      };
    case 'supplySideMixer':
      return {
        description: 'Air Loop Supply Side Mixer'
      };
    case 'connectorSplitter':
      return {
        description: 'Plant Loop Splitter'
      };
    case 'zoneSplitter':
      return {
        description: 'Air Loop Zone Splitter'
      };
    case 'supplySideSplitter':
      return {
        description: 'Air Loop Supply Side Splitter'
      };
    case 'fanConstantVolume':
      return {
        fanEfficiency: 0.7,
        pressureRise: 500, // Pa
        motorEfficiency: 0.9,
        maximumFlowRate: 1.0 // m³/s
      };
    case 'fanVariableVolume':
      return {
        fanEfficiency: 0.7,
        pressureRise: 500, // Pa
        motorEfficiency: 0.9,
        maximumFlowRate: 1.0 // m³/s
      };
    case 'coilCoolingWater':
      return {
        designWaterFlowRate: 0.0015, // m³/s
        designAirFlowRate: 1.0, // m³/s
        designInletWaterTemperature: 7.0, // °C
        designInletAirTemperature: 25.0, // °C
        designOutletAirTemperature: 10.0 // °C
      };
    case 'coilHeatingWater':
      return {
        designWaterFlowRate: 0.0015, // m³/s
        designAirFlowRate: 1.0, // m³/s
        designInletWaterTemperature: 60.0, // °C
        designInletAirTemperature: 10.0, // °C
        designOutletAirTemperature: 40.0 // °C
      };
    case 'outdoorAirSystem':
      return {
        controllerName: 'OA Controller',
        minimumOutdoorAirFlowRate: 500, // CFM
        maximumOutdoorAirFlowRate: 2000 // CFM
      };
    default:
      return {};
  }
};

export default {
  getComponentMetadata,
  getDefaultProperties,
  getComponentMetadataSync,
  getDefaultPropertiesSync,
  getComponentTypeMap
};
