import React from 'react';
import { Position } from 'reactflow';

// Connector Splitter Node Configuration (for PlantLoop)
export const connectorSplitterConfig = {
  componentType: 'OS:Connector:Splitter',
  nodeType: 'connector-splitter',
  inputs: [
    {
      id: 'inlet',
      position: Position.Left,
      className: 'water-handle',
      style: { top: '50%' }
    }
  ],
  outputs: [
    {
      id: 'outlet-1',
      position: Position.Right,
      className: 'water-handle',
      style: { top: '30%' }
    },
    {
      id: 'outlet-2',
      position: Position.Right,
      className: 'water-handle',
      style: { top: '70%' }
    },
    {
      id: 'outlet-3',
      position: Position.Bottom,
      className: 'water-handle',
      style: { left: '70%' }
    }
  ],
  displayProperties: [
    {
      name: 'description',
      label: 'Description',
      unit: ''
    }
  ],
  icon: (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M4 12L14 12" stroke="#9575cd" strokeWidth="2"/>
      <path d="M14 12L20 6" stroke="#9575cd" strokeWidth="2"/>
      <path d="M14 12L20 18" stroke="#9575cd" strokeWidth="2"/>
      <circle cx="14" cy="12" r="2" fill="#9575cd"/>
    </svg>
  )
};

// Zone Splitter Node Configuration (for AirLoopHVAC)
export const zoneSplitterConfig = {
  componentType: 'OS:AirLoopHVAC:ZoneSplitter',
  nodeType: 'zone-splitter',
  inputs: [
    {
      id: 'inlet',
      position: Position.Left,
      className: 'air-handle',
      style: { top: '50%' }
    }
  ],
  outputs: [
    {
      id: 'outlet-1',
      position: Position.Right,
      className: 'air-handle',
      style: { top: '30%' }
    },
    {
      id: 'outlet-2',
      position: Position.Right,
      className: 'air-handle',
      style: { top: '70%' }
    },
    {
      id: 'outlet-3',
      position: Position.Bottom,
      className: 'air-handle',
      style: { left: '70%' }
    }
  ],
  displayProperties: [
    {
      name: 'description',
      label: 'Description',
      unit: ''
    }
  ],
  icon: (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M4 12L14 12" stroke="#64b5f6" strokeWidth="2"/>
      <path d="M14 12L20 6" stroke="#64b5f6" strokeWidth="2"/>
      <path d="M14 12L20 18" stroke="#64b5f6" strokeWidth="2"/>
      <circle cx="14" cy="12" r="2" fill="#64b5f6"/>
    </svg>
  )
};

// Supply Side Splitter Node Configuration (for AirLoopHVAC)
export const supplySideSplitterConfig = {
  componentType: 'OS:Connector:Splitter',
  nodeType: 'supply-side-splitter',
  inputs: [
    {
      id: 'inlet',
      position: Position.Left,
      className: 'air-handle',
      style: { top: '50%' }
    }
  ],
  outputs: [
    {
      id: 'outlet-1',
      position: Position.Right,
      className: 'air-handle',
      style: { top: '30%' }
    },
    {
      id: 'outlet-2',
      position: Position.Right,
      className: 'air-handle',
      style: { top: '70%' }
    }
  ],
  displayProperties: [
    {
      name: 'description',
      label: 'Description',
      unit: ''
    }
  ],
  icon: (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M4 12L14 12" stroke="#64b5f6" strokeWidth="2"/>
      <path d="M14 12L20 6" stroke="#64b5f6" strokeWidth="2"/>
      <path d="M14 12L20 18" stroke="#64b5f6" strokeWidth="2"/>
      <circle cx="14" cy="12" r="2" fill="#64b5f6"/>
      <text x="2" y="3" fontSize="3" fill="#64b5f6">Supply</text>
    </svg>
  )
};

export default {
  connectorSplitterConfig,
  zoneSplitterConfig,
  supplySideSplitterConfig
};
