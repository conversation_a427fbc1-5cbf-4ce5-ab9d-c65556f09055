import React, { memo } from 'react';
import { Handle, Position } from 'reactflow';

const BoilerNode = ({ data, selected }) => {
  const { label, properties } = data;
  
  return (
    <div className={`boiler-node ${selected ? 'selected' : ''}`}>
      {/* Input handles */}
      <Handle
        type="target"
        position={Position.Left}
        id="hot-water-in"
        className="handle hot-water-handle"
        style={{ top: '50%' }}
      />
      
      <div className="node-content">
        <div className="node-header">{label}</div>
        <div className="node-body">
          <div className="node-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect x="4" y="3" width="16" height="18" rx="2" stroke="#ff8a65" strokeWidth="2"/>
              <path d="M8 8C8 8 10 6 12 8C14 10 16 8 16 8" stroke="#ff8a65" strokeWidth="2"/>
              <path d="M8 12C8 12 10 10 12 12C14 14 16 12 16 12" stroke="#ff8a65" strokeWidth="2"/>
              <path d="M8 16C8 16 10 14 12 16C14 18 16 16 16 16" stroke="#ff8a65" strokeWidth="2"/>
            </svg>
          </div>
          <div className="node-details">
            <div className="property-row">
              <span className="property-label">Capacity:</span>
              <span className="property-value">{properties.capacity / 1000} MBH</span>
            </div>
            <div className="property-row">
              <span className="property-label">Efficiency:</span>
              <span className="property-value">{(properties.efficiency * 100).toFixed(1)}%</span>
            </div>
          </div>
        </div>
      </div>
      
      {/* Output handles */}
      <Handle
        type="source"
        position={Position.Right}
        id="hot-water-out"
        className="handle hot-water-handle"
        style={{ top: '50%' }}
      />
    </div>
  );
};

export default memo(BoilerNode);

