import React, { memo } from 'react';
import { Handle, Position } from 'reactflow';

const CoolingTowerNode = ({ data, selected }) => {
  const { label, properties } = data;
  
  return (
    <div className={`cooling-tower-node ${selected ? 'selected' : ''}`}>
      {/* Input handles */}
      <Handle
        type="target"
        position={Position.Bottom}
        id="condenser-water-in"
        className="handle condenser-water-handle"
        style={{ left: '50%' }}
      />
      
      <div className="node-content">
        <div className="node-header">{label}</div>
        <div className="node-body">
          <div className="node-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 3L4 10H20L12 3Z" stroke="#81c784" strokeWidth="2"/>
              <rect x="5" y="10" width="14" height="10" stroke="#81c784" strokeWidth="2"/>
              <path d="M8 14H16" stroke="#81c784" strokeWidth="2"/>
              <path d="M8 18H16" stroke="#81c784" strokeWidth="2"/>
            </svg>
          </div>
          <div className="node-details">
            <div className="property-row">
              <span className="property-label">Capacity:</span>
              <span className="property-value">{properties.capacity} Tons</span>
            </div>
            <div className="property-row">
              <span className="property-label">Fan Power:</span>
              <span className="property-value">{properties.fanPower} HP</span>
            </div>
          </div>
        </div>
      </div>
      
      {/* Output handles */}
      <Handle
        type="source"
        position={Position.Top}
        id="condenser-water-out"
        className="handle condenser-water-handle"
        style={{ left: '50%' }}
      />
    </div>
  );
};

export default memo(CoolingTowerNode);

