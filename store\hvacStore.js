import { create } from 'zustand';
import {
  applyNodeChanges,
  applyEdgeChanges,
  addEdge
} from 'reactflow';
import { v4 as uuidv4 } from 'uuid';
import { getDefaultPropertiesSync } from '../utils/iddParser';
// Import API service but use mock data for now
// import { saveComponentPropertiesAPI } from '../services/api';

export const useHVACStore = create((set, get) => ({
  // State
  nodes: [],
  edges: [],
  selectedNode: null,

  // Node operations
  onNodesChange: (changes) => {
    set({
      nodes: applyNodeChanges(changes, get().nodes),
    });
  },

  // Edge operations
  onEdgesChange: (changes) => {
    set({
      edges: applyEdgeChanges(changes, get().edges),
    });
  },

  // Connection operations
  onConnect: (connection) => {
    // Validate connection before adding
    const sourceNode = get().nodes.find(node => node.id === connection.source);
    const targetNode = get().nodes.find(node => node.id === connection.target);

    if (!sourceNode || !targetNode) {
      console.error('Invalid connection: source or target node not found');
      return;
    }

    // Check if connection is valid based on component types
    if (!isValidConnection(sourceNode.type, targetNode.type, connection.sourceHandle, connection.targetHandle)) {
      console.error('Invalid connection: incompatible component types or connection points');
      return;
    }

    // Add edge with appropriate type based on components
    const edgeType = determineEdgeType(sourceNode.type, targetNode.type, connection.sourceHandle, connection.targetHandle);

    set({
      edges: addEdge(
        {
          ...connection,
          type: edgeType,
          data: { label: `${edgeType}` }
        },
        get().edges
      ),
    });
  },

  // Node selection
  onNodeSelect: (node) => {
    set({ selectedNode: node });
  },

  // Add new node
  addNode: (type, position, parentId = null, side = null) => {
    // Get the component label
    const componentLabel = getComponentLabel(type);

    // Get the count of nodes of this type
    const count = get().nodes.filter(n => n.type === type).length + 1;

    // Create the new node
    const newNode = {
      id: uuidv4(),
      type,
      position,
      // If parentId is provided, set it as the parent node
      ...(parentId && { parentId }),
      // Set extent to 'parent' if this is a child node to keep it within the parent
      ...(parentId && { extent: 'parent' }),
      // Set zIndex to ensure child nodes appear above parent nodes
      zIndex: parentId ? 1 : 0,
      data: {
        label: `${componentLabel} ${count}`,
        properties: getDefaultPropertiesSync(type),
        // If side is provided, include it in the node data
        ...(side && { side })
      }
    };

    // Log the node creation for debugging
    console.log(`Creating new node:`, {
      id: newNode.id,
      type: newNode.type,
      position: newNode.position,
      parentId: newNode.parentId,
      side: side
    });

    set({
      nodes: [...get().nodes, newNode]
    });

    return newNode;
  },

  // Remove node
  removeNode: (nodeId) => {
    // Remove all connected edges
    const edgesToRemove = get().edges.filter(
      edge => edge.source === nodeId || edge.target === nodeId
    );

    set({
      nodes: get().nodes.filter(node => node.id !== nodeId),
      edges: get().edges.filter(
        edge => edge.source !== nodeId && edge.target !== nodeId
      ),
      selectedNode: get().selectedNode?.id === nodeId ? null : get().selectedNode
    });
  },

  // Update node properties
  updateNodeProperties: (nodeId, properties) => {
    set({
      nodes: get().nodes.map(node => {
        if (node.id === nodeId) {
          return {
            ...node,
            data: {
              ...node.data,
              properties: {
                ...node.data.properties,
                ...properties
              }
            }
          };
        }
        return node;
      }),
      selectedNode: get().selectedNode?.id === nodeId
        ? {
            ...get().selectedNode,
            data: {
              ...get().selectedNode.data,
              properties: {
                ...get().selectedNode.data.properties,
                ...properties
              }
            }
          }
        : get().selectedNode
    });
  },

  // Save component properties to backend
  saveComponentProperties: async (nodeId) => {
    try {
      const node = get().nodes.find(node => node.id === nodeId);
      if (!node) {
        return { success: false, error: 'Node not found' };
      }

      // In a real implementation, this would call the backend API
      // For now, we'll just log the properties and use a mock response
      console.log('Saving component properties:', {
        nodeId,
        properties: node.data.properties
      });

      // Uncomment to use the actual API
      // const result = await saveComponentPropertiesAPI(nodeId, node.data.properties);
      // return result;

      // Mock successful response
      return { success: true };
    } catch (error) {
      console.error('Error saving component properties:', error);
      return { success: false, error: error.message };
    }
  },

  // Calculate system performance
  calculateSystemPerformance: async () => {
    const system = {
      nodes: get().nodes,
      edges: get().edges
    };

    try {
      // In a real implementation, this would call the backend API
      // For now, we'll just return a mock result
      return mockCalculateEnergy(system);
    } catch (error) {
      console.error('Error calculating system performance:', error);
      return null;
    }
  },

  // Save system
  saveSystem: async (name) => {
    const system = {
      name,
      nodes: get().nodes,
      edges: get().edges,
      timestamp: new Date().toISOString()
    };

    try {
      // In a real implementation, this would call the backend API
      // For now, we'll just log the system
      console.log('Saving system:', system);
      localStorage.setItem('hvac-system', JSON.stringify(system));
      return { success: true, id: uuidv4() };
    } catch (error) {
      console.error('Error saving system:', error);
      return { success: false, error: error.message };
    }
  },

  // Load system
  loadSystem: async () => {
    try {
      // In a real implementation, this would call the backend API
      // For now, we'll just load from localStorage
      const savedSystem = localStorage.getItem('hvac-system');
      if (savedSystem) {
        const system = JSON.parse(savedSystem);
        set({
          nodes: system.nodes,
          edges: system.edges,
          selectedNode: null
        });
        return { success: true };
      }
      return { success: false, error: 'No saved system found' };
    } catch (error) {
      console.error('Error loading system:', error);
      return { success: false, error: error.message };
    }
  },

  // Clear system
  clearSystem: () => {
    set({
      nodes: [],
      edges: [],
      selectedNode: null
    });
  },

  // Export system as JSON
  exportSystemAsJSON: () => {
    const system = {
      nodes: get().nodes,
      edges: get().edges,
      timestamp: new Date().toISOString()
    };

    // Create a Blob with the JSON data
    const blob = new Blob([JSON.stringify(system, null, 2)], { type: 'application/json' });

    // Create a download link and trigger the download
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `hvac-system-${new Date().toISOString().slice(0, 10)}.json`;
    document.body.appendChild(link);
    link.click();

    // Clean up
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    return { success: true };
  },

  // Import system from JSON
  importSystemFromJSON: async (jsonFile) => {
    try {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();

        reader.onload = (event) => {
          try {
            const system = JSON.parse(event.target.result);

            // Validate the imported data
            if (!system.nodes || !Array.isArray(system.nodes) || !system.edges || !Array.isArray(system.edges)) {
              reject({ success: false, error: 'Invalid system data format' });
              return;
            }

            // Update the store with the imported data
            set({
              nodes: system.nodes,
              edges: system.edges,
              selectedNode: null
            });

            resolve({ success: true });
          } catch (error) {
            console.error('Error parsing JSON:', error);
            reject({ success: false, error: 'Failed to parse JSON file' });
          }
        };

        reader.onerror = () => {
          reject({ success: false, error: 'Failed to read file' });
        };

        reader.readAsText(jsonFile);
      });
    } catch (error) {
      console.error('Error importing system:', error);
      return { success: false, error: error.message };
    }
  }
}));

// Helper function to get component label
function getComponentLabel(type) {
  // Handle OpenStudio component types
  if (type.startsWith('OS:')) {
    // Extract the component name from the OpenStudio type
    const parts = type.split(':');
    if (parts.length > 1) {
      // Format the last part of the type as a readable label
      const lastPart = parts[parts.length - 1];
      return lastPart.replace(/([A-Z])/g, ' $1').trim();
    }
    return type;
  }

  // Handle internal component types
  switch (type) {
    case 'airHandler':
      return 'Air Handler';
    case 'chiller':
      return 'Chiller';
    case 'boiler':
      return 'Boiler';
    case 'coolingTower':
      return 'Cooling Tower';
    case 'pump':
      return 'Pump';
    case 'vav':
      return 'VAV Box';
    case 'zone':
      return 'Zone';
    case 'airLoopHVAC':
      return 'Air Loop';
    case 'plantLoop':
      return 'Plant Loop';
    case 'fanConstantVolume':
      return 'Constant Fan';
    case 'fanVariableVolume':
      return 'Variable Fan';
    case 'coilCoolingWater':
      return 'Cooling Coil';
    case 'coilHeatingWater':
      return 'Heating Coil';
    case 'outdoorAirSystem':
      return 'Outdoor Air';
    case 'connectorMixer':
      return 'Water Mixer';
    case 'zoneMixer':
      return 'Zone Mixer';
    case 'supplySideMixer':
      return 'Supply Mixer';
    case 'connectorSplitter':
      return 'Water Splitter';
    case 'zoneSplitter':
      return 'Zone Splitter';
    case 'supplySideSplitter':
      return 'Supply Splitter';
    default:
      return 'Component';
  }
}

// Helper functions for component properties are now imported from iddParser.js

// Helper function to determine if a connection is valid
function isValidConnection(sourceType, targetType, sourceHandle, targetHandle) {
  // Check connection based on handle types
  if (sourceHandle && targetHandle) {
    // Air connections
    if (
      (sourceHandle.includes('air') && !targetHandle.includes('air')) ||
      (!sourceHandle.includes('air') && targetHandle.includes('air'))
    ) {
      return false;
    }

    // Water connections
    if (
      (sourceHandle.includes('water') && !targetHandle.includes('water')) ||
      (!sourceHandle.includes('water') && targetHandle.includes('water'))
    ) {
      return false;
    }

    // Chilled water connections
    if (
      (sourceHandle.includes('chilled') && !targetHandle.includes('chilled')) ||
      (!sourceHandle.includes('chilled') && targetHandle.includes('chilled'))
    ) {
      return false;
    }

    // Hot water connections
    if (
      (sourceHandle.includes('hot') && !targetHandle.includes('hot')) ||
      (!sourceHandle.includes('hot') && targetHandle.includes('hot'))
    ) {
      return false;
    }

    // Condenser water connections
    if (
      (sourceHandle.includes('condenser') && !targetHandle.includes('condenser')) ||
      (!sourceHandle.includes('condenser') && targetHandle.includes('condenser'))
    ) {
      return false;
    }
  }

  // Define valid connections between component types
  const validConnections = {
    // Air side components
    'airHandler': ['vav', 'zone', 'airLoopHVAC', 'zoneMixer', 'zoneSplitter', 'supplySideMixer', 'supplySideSplitter'],
    'vav': ['zone', 'zoneMixer', 'zoneSplitter'],
    'zone': ['zoneMixer'],

    // Air loop components
    'airLoopHVAC': ['airHandler', 'outdoorAirSystem', 'zoneMixer', 'zoneSplitter', 'supplySideMixer', 'supplySideSplitter', 'fanConstantVolume', 'fanVariableVolume', 'coilCoolingWater', 'coilHeatingWater'],
    'outdoorAirSystem': ['airLoopHVAC'],

    // Air equipment components
    'fanConstantVolume': ['airLoopHVAC', 'fanVariableVolume', 'coilCoolingWater', 'coilHeatingWater'],
    'fanVariableVolume': ['airLoopHVAC', 'fanConstantVolume', 'coilCoolingWater', 'coilHeatingWater'],
    'coilCoolingWater': ['airLoopHVAC', 'fanConstantVolume', 'fanVariableVolume', 'coilHeatingWater', 'plantLoop'],
    'coilHeatingWater': ['airLoopHVAC', 'fanConstantVolume', 'fanVariableVolume', 'coilCoolingWater', 'plantLoop'],

    // Air connectors
    'zoneMixer': ['airLoopHVAC', 'airHandler', 'vav', 'zone', 'zoneSplitter'],
    'zoneSplitter': ['airLoopHVAC', 'airHandler', 'vav', 'zoneMixer'],
    'supplySideMixer': ['airLoopHVAC', 'supplySideSplitter'],
    'supplySideSplitter': ['airLoopHVAC', 'supplySideMixer'],

    // Water side components
    'chiller': ['pump', 'coolingTower', 'plantLoop', 'connectorMixer', 'connectorSplitter'],
    'boiler': ['pump', 'plantLoop', 'connectorMixer', 'connectorSplitter'],
    'coolingTower': ['chiller', 'pump', 'plantLoop', 'connectorMixer', 'connectorSplitter'],
    'pump': ['chiller', 'boiler', 'coolingTower', 'plantLoop', 'connectorMixer', 'connectorSplitter'],

    // Plant loop components
    'plantLoop': ['chiller', 'boiler', 'coolingTower', 'pump', 'connectorMixer', 'connectorSplitter', 'coilCoolingWater', 'coilHeatingWater'],

    // Water connectors
    'connectorMixer': ['plantLoop', 'chiller', 'boiler', 'coolingTower', 'pump', 'connectorSplitter'],
    'connectorSplitter': ['plantLoop', 'chiller', 'boiler', 'coolingTower', 'pump', 'connectorMixer']
  };

  return validConnections[sourceType]?.includes(targetType) || false;
}

// Helper function to determine edge type based on connected components
function determineEdgeType(sourceType, targetType, sourceHandle, targetHandle) {
  // Determine edge type based on handles
  if (sourceHandle && targetHandle) {
    if (sourceHandle.includes('air') && targetHandle.includes('air')) {
      return 'airFlow';
    }

    if (sourceHandle.includes('chilled') && targetHandle.includes('chilled')) {
      return 'chilledWaterFlow';
    }

    if (sourceHandle.includes('hot') && targetHandle.includes('hot')) {
      return 'hotWaterFlow';
    }

    if (sourceHandle.includes('condenser') && targetHandle.includes('condenser')) {
      return 'condenserWaterFlow';
    }

    if (sourceHandle.includes('water') && targetHandle.includes('water')) {
      return 'waterFlow';
    }
  }

  // Fallback to determining edge type based on component types

  // Air connections
  if (
    // Air handler connections
    (sourceType === 'airHandler' && (targetType === 'vav' || targetType === 'zone' || targetType === 'zoneMixer' || targetType === 'zoneSplitter' || targetType === 'supplySideMixer' || targetType === 'supplySideSplitter')) ||
    // Air loop connections
    (sourceType === 'airLoopHVAC' && (targetType === 'airHandler' || targetType === 'zoneMixer' || targetType === 'zoneSplitter' || targetType === 'supplySideMixer' || targetType === 'supplySideSplitter' || targetType === 'outdoorAirSystem' || targetType === 'fanConstantVolume' || targetType === 'fanVariableVolume' || targetType === 'coilCoolingWater' || targetType === 'coilHeatingWater')) ||
    (sourceType === 'outdoorAirSystem' && targetType === 'airLoopHVAC') ||
    // VAV and zone connections
    (sourceType === 'vav' && (targetType === 'zone' || targetType === 'zoneMixer' || targetType === 'zoneSplitter')) ||
    // Zone mixer connections
    (sourceType === 'zoneMixer' && (targetType === 'airLoopHVAC' || targetType === 'airHandler' || targetType === 'zoneSplitter')) ||
    (sourceType === 'zoneSplitter' && (targetType === 'vav' || targetType === 'zoneMixer' || targetType === 'airHandler' || targetType === 'airLoopHVAC')) ||
    // Supply side mixer/splitter connections
    (sourceType === 'supplySideMixer' && (targetType === 'airLoopHVAC' || targetType === 'supplySideSplitter')) ||
    (sourceType === 'supplySideSplitter' && (targetType === 'airLoopHVAC' || targetType === 'supplySideMixer')) ||
    // Zone to mixer connection
    (sourceType === 'zone' && targetType === 'zoneMixer') ||
    // Fan connections
    (sourceType === 'fanConstantVolume' && (targetType === 'airLoopHVAC' || targetType === 'fanVariableVolume' || targetType === 'coilCoolingWater' || targetType === 'coilHeatingWater')) ||
    (sourceType === 'fanVariableVolume' && (targetType === 'airLoopHVAC' || targetType === 'fanConstantVolume' || targetType === 'coilCoolingWater' || targetType === 'coilHeatingWater')) ||
    // Coil air connections
    (sourceType === 'coilCoolingWater' && (targetType === 'airLoopHVAC' || targetType === 'fanConstantVolume' || targetType === 'fanVariableVolume' || targetType === 'coilHeatingWater')) ||
    (sourceType === 'coilHeatingWater' && (targetType === 'airLoopHVAC' || targetType === 'fanConstantVolume' || targetType === 'fanVariableVolume' || targetType === 'coilCoolingWater'))
  ) {
    return 'airFlow';
  }

  // Chilled water connections
  if (
    (sourceType === 'chiller' && (targetType === 'pump' || targetType === 'plantLoop' || targetType === 'connectorMixer' || targetType === 'connectorSplitter')) ||
    (sourceType === 'pump' && targetType === 'chiller') ||
    (sourceType === 'plantLoop' && targetType === 'chiller') ||
    (sourceType === 'connectorMixer' && targetType === 'chiller') ||
    (sourceType === 'connectorSplitter' && targetType === 'chiller')
  ) {
    return 'chilledWaterFlow';
  }

  // Hot water connections
  if (
    (sourceType === 'boiler' && (targetType === 'pump' || targetType === 'plantLoop' || targetType === 'connectorMixer' || targetType === 'connectorSplitter')) ||
    (sourceType === 'pump' && targetType === 'boiler') ||
    (sourceType === 'plantLoop' && targetType === 'boiler') ||
    (sourceType === 'connectorMixer' && targetType === 'boiler') ||
    (sourceType === 'connectorSplitter' && targetType === 'boiler')
  ) {
    return 'hotWaterFlow';
  }

  // Condenser water connections
  if (
    (sourceType === 'coolingTower' && (targetType === 'chiller' || targetType === 'pump' || targetType === 'plantLoop' || targetType === 'connectorMixer' || targetType === 'connectorSplitter')) ||
    (sourceType === 'pump' && targetType === 'coolingTower') ||
    (sourceType === 'plantLoop' && targetType === 'coolingTower') ||
    (sourceType === 'connectorMixer' && targetType === 'coolingTower') ||
    (sourceType === 'connectorSplitter' && targetType === 'coolingTower')
  ) {
    return 'condenserWaterFlow';
  }

  // Plant loop connections
  if (
    (sourceType === 'plantLoop' && (targetType === 'pump' || targetType === 'connectorMixer' || targetType === 'connectorSplitter' || targetType === 'coilCoolingWater' || targetType === 'coilHeatingWater')) ||
    (sourceType === 'pump' && targetType === 'plantLoop') ||
    (sourceType === 'connectorMixer' && targetType === 'plantLoop') ||
    (sourceType === 'connectorSplitter' && targetType === 'plantLoop') ||
    (sourceType === 'coilCoolingWater' && targetType === 'plantLoop') ||
    (sourceType === 'coilHeatingWater' && targetType === 'plantLoop')
  ) {
    return 'waterFlow';
  }

  // Mixer and splitter connections
  if (
    (sourceType === 'connectorMixer' && targetType === 'connectorSplitter') ||
    (sourceType === 'connectorSplitter' && targetType === 'connectorMixer')
  ) {
    return 'waterFlow';
  }

  // Default
  return 'default';
}

// Mock energy calculation function
function mockCalculateEnergy(system) {
  // Get all zones
  const zones = system.nodes.filter(node => node.type === 'zone');

  // Calculate total loads
  const totalCoolingLoad = zones.reduce(
    (sum, zone) => sum + zone.data.properties.peakCoolingLoad,
    0
  );

  const totalHeatingLoad = zones.reduce(
    (sum, zone) => sum + zone.data.properties.peakHeatingLoad,
    0
  );

  // Get all equipment
  const chillers = system.nodes.filter(node => node.type === 'chiller');
  const boilers = system.nodes.filter(node => node.type === 'boiler');
  const airHandlers = system.nodes.filter(node => node.type === 'airHandler');
  const airLoops = system.nodes.filter(node => node.type === 'airLoopHVAC');
  const plantLoops = system.nodes.filter(node => node.type === 'plantLoop');
  const outdoorAirSystems = system.nodes.filter(node => node.type === 'outdoorAirSystem');
  const pumps = system.nodes.filter(node => node.type === 'pump');
  const fans = system.nodes.filter(node => node.type === 'fanConstantVolume' || node.type === 'fanVariableVolume');
  const coolingCoils = system.nodes.filter(node => node.type === 'coilCoolingWater');
  const heatingCoils = system.nodes.filter(node => node.type === 'coilHeatingWater');

  // Calculate capacities
  const coolingCapacity = chillers.reduce(
    (sum, chiller) => sum + chiller.data.properties.capacity * 12000, // Convert tons to BTU/h
    0
  );

  const heatingCapacity = boilers.reduce(
    (sum, boiler) => sum + boiler.data.properties.capacity,
    0
  );

  // Calculate energy consumption (simplified)
  const annualCoolingEnergy = totalCoolingLoad * 1200 * 0.000293071 / 3.0; // kWh
  const annualHeatingEnergy = totalHeatingLoad * 800 * 0.000293071 / 0.85; // kWh

  const annualFanEnergy = airHandlers.reduce(
    (sum, ahu) => sum + ahu.data.properties.fanPower * 0.7457 * 3000, // kWh
    0
  ) + airLoops.reduce(
    (sum, airLoop) => sum + (airLoop.data.properties.designSupplyAirFlowRate / 2000) * 0.7457 * 3000, // kWh (assuming 1 HP per 2000 CFM)
    0
  ) + fans.reduce(
    (sum, fan) => {
      // Convert fan power based on efficiency, pressure rise and flow rate
      // P = Q * ΔP / (η * 1000) where P is power in kW, Q is flow rate in m³/s, ΔP is pressure rise in Pa, η is efficiency
      const flowRate = fan.data.properties.maximumFlowRate; // m³/s
      const pressureRise = fan.data.properties.pressureRise; // Pa
      const efficiency = fan.data.properties.fanEfficiency * fan.data.properties.motorEfficiency;
      const power = flowRate * pressureRise / (efficiency * 1000); // kW
      return sum + power * 3000; // kWh (assuming 3000 hours of operation)
    },
    0
  );

  const annualPumpEnergy = pumps.reduce(
    (sum, pump) => sum + pump.data.properties.power * 0.7457 * 3000, // kWh
    0
  ) + plantLoops.reduce(
    (sum, plantLoop) => sum + (plantLoop.data.properties.maximumLoopFlowRate / 100) * 0.7457 * 3000, // kWh (assuming 1 HP per 100 GPM)
    0
  );

  const totalAnnualEnergy = annualCoolingEnergy + annualHeatingEnergy + annualFanEnergy + annualPumpEnergy;

  // Calculate efficiency metrics
  const copCooling = chillers.length > 0
    ? chillers.reduce((sum, chiller) => sum + chiller.data.properties.cop, 0) / chillers.length * 0.85
    : 0;

  const copHeating = boilers.length > 0
    ? boilers.reduce((sum, boiler) => sum + boiler.data.properties.efficiency, 0) / boilers.length
    : 0;

  // Calculate energy cost
  const electricityCost = 0.12; // $/kWh
  const annualEnergyCost = totalAnnualEnergy * electricityCost;

  return {
    loads: {
      total_cooling_load: totalCoolingLoad,
      total_heating_load: totalHeatingLoad
    },
    capacities: {
      cooling_capacity: coolingCapacity,
      heating_capacity: heatingCapacity,
      cooling_capacity_ratio: totalCoolingLoad > 0 ? coolingCapacity / totalCoolingLoad : 0,
      heating_capacity_ratio: totalHeatingLoad > 0 ? heatingCapacity / totalHeatingLoad : 0
    },
    energy: {
      annual_cooling_energy: annualCoolingEnergy,
      annual_heating_energy: annualHeatingEnergy,
      annual_fan_energy: annualFanEnergy,
      annual_pump_energy: annualPumpEnergy,
      total_annual_energy: totalAnnualEnergy
    },
    efficiency: {
      cop_cooling: copCooling,
      cop_heating: copHeating
    },
    cost: {
      annual_energy_cost: annualEnergyCost
    }
  };
}
