import React, { memo } from 'react';
import { Handle, Position } from 'reactflow';

const AirHandlerNode = ({ data, selected }) => {
  const { label, properties } = data;

  return (
    <div className={`air-handler-node ${selected ? 'selected' : ''}`}>
      {/* Input handles */}
      <Handle
        type="target"
        position={Position.Left}
        id="chilled-water-in"
        className="handle chilled-water-handle"
        style={{ top: '30%' }}
      />
      <Handle
        type="target"
        position={Position.Left}
        id="hot-water-in"
        className="handle hot-water-handle"
        style={{ top: '70%' }}
      />

      <div className="node-content">
        <div className="node-header">{label}</div>
        <div className="node-body">
          <div className="node-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect x="2" y="4" width="20" height="16" rx="2" stroke="#64b5f6" strokeWidth="2"/>
              <path d="M6 8L18 8" stroke="#64b5f6" strokeWidth="2"/>
              <path d="M6 12L18 12" stroke="#64b5f6" strokeWidth="2"/>
              <path d="M6 16L18 16" stroke="#64b5f6" strokeWidth="2"/>
            </svg>
          </div>
          <div className="node-details">
            <div className="property-row">
              <span className="property-label">Airflow:</span>
              <span className="property-value">{properties.airflow} CFM</span>
            </div>
            <div className="property-row">
              <span className="property-label">Cooling:</span>
              <span className="property-value">{properties.coolingCapacity / 12000} Tons</span>
            </div>
          </div>
        </div>
      </div>

      {/* Output handles */}
      <Handle
        type="source"
        position={Position.Right}
        id="supply-air"
        className="handle air-handle"
        style={{ top: '30%' }}
      />
      <Handle
        type="source"
        position={Position.Right}
        id="return-air"
        className="handle air-handle"
        style={{ top: '70%' }}
      />

      {/* Bottom handles for chilled/hot water return */}
      <Handle
        type="source"
        position={Position.Bottom}
        id="chilled-water-out"
        className="handle chilled-water-handle"
        style={{ left: '30%' }}
      />
      <Handle
        type="source"
        position={Position.Bottom}
        id="hot-water-out"
        className="handle hot-water-handle"
        style={{ left: '70%' }}
      />
    </div>
  );
};

export default memo(AirHandlerNode);

