import json
import logging
from typing import Dict, List, Any

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

def calculate_energy(event, context):
    """AWS Lambda handler for energy calculations"""
    try:
        # Parse request body
        body = json.loads(event['body'])
        nodes = body.get('nodes', [])
        edges = body.get('edges', [])
        
        # Build system graph
        system_graph = build_system_graph(nodes, edges)
        
        # Validate system configuration
        validation_result = validate_system(system_graph)
        if not validation_result['valid']:
            return {
                'statusCode': 400,
                'headers': {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                },
                'body': json.dumps({
                    'success': False,
                    'errors': validation_result['errors']
                })
            }
        
        # Perform energy calculations
        calculation_results = calculate_energy_performance(system_graph)
        
        return {
            'statusCode': 200,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps({
                'success': True,
                'results': calculation_results
            })
        }
    except Exception as e:
        logger.error(f"Error in energy calculation: {str(e)}")
        return {
            'statusCode': 500,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps({
                'success': False,
                'error': str(e)
            })
        }

def build_system_graph(nodes: List[Dict], edges: List[Dict]) -> Dict:
    """
    Build a graph representation of the HVAC system
    
    Args:
        nodes: List of node objects from ReactFlow
        edges: List of edge objects from ReactFlow
        
    Returns:
        Dictionary containing the system graph representation
    """
    graph = {
        'nodes': {},
        'adjacency': {},
        'reverse_adjacency': {}
    }
    
    # Add nodes to the graph
    for node in nodes:
        node_id = node['id']
        node_type = node['type']
        properties = node.get('data', {}).get('properties', {})
        
        graph['nodes'][node_id] = {
            'type': node_type,
            'properties': properties
        }
        graph['adjacency'][node_id] = []
        graph['reverse_adjacency'][node_id] = []
    
    # Add edges to the graph
    for edge in edges:
        source = edge['source']
        target = edge['target']
        edge_type = edge.get('type', 'default')
        
        if source in graph['adjacency']:
            graph['adjacency'][source].append({
                'target': target,
                'type': edge_type
            })
        
        if target in graph['reverse_adjacency']:
            graph['reverse_adjacency'][target].append({
                'source': source,
                'type': edge_type
            })
    
    return graph

def validate_system(system_graph: Dict) -> Dict:
    """
    Validate the HVAC system configuration
    
    Args:
        system_graph: Dictionary containing the system graph
        
    Returns:
        Dictionary with validation results
    """
    errors = []
    
    # Check if system has at least one component
    if len(system_graph['nodes']) == 0:
        errors.append("System has no components")
        return {'valid': False, 'errors': errors}
    
    # Check for disconnected components
    for node_id, node_data in system_graph['nodes'].items():
        if (len(system_graph['adjacency'][node_id]) == 0 and 
            len(system_graph['reverse_adjacency'][node_id]) == 0):
            errors.append(f"Component {node_id} ({node_data['type']}) is disconnected")
    
    # Check for zones without air supply
    for node_id, node_data in system_graph['nodes'].items():
        if node_data['type'] == 'zone':
            if len(system_graph['reverse_adjacency'][node_id]) == 0:
                errors.append(f"Zone {node_id} has no air supply")
    
    # Check for air handlers without chilled water
    for node_id, node_data in system_graph['nodes'].items():
        if node_data['type'] == 'airHandler':
            has_chilled_water = False
            for connection in system_graph['reverse_adjacency'][node_id]:
                source_id = connection['source']
                source_type = system_graph['nodes'][source_id]['type']
                if source_type == 'chiller' or connection['type'] == 'chilledWaterFlow':
                    has_chilled_water = True
                    break
            
            if not has_chilled_water:
                errors.append(f"Air handler {node_id} has no chilled water supply")
    
    # Check for chillers without condenser water (if water-cooled)
    for node_id, node_data in system_graph['nodes'].items():
        if node_data['type'] == 'chiller' and node_data['properties'].get('type') == 'water-cooled':
            has_condenser_water = False
            for connection in system_graph['reverse_adjacency'][node_id]:
                source_id = connection['source']
                source_type = system_graph['nodes'][source_id]['type']
                if source_type == 'coolingTower' or connection['type'] == 'condenserWaterFlow':
                    has_condenser_water = True
                    break
            
            if not has_condenser_water:
                errors.append(f"Water-cooled chiller {node_id} has no condenser water supply")
    
    return {'valid': len(errors) == 0, 'errors': errors}

def calculate_energy_performance(system_graph: Dict) -> Dict:
    """
    Calculate energy performance of the HVAC system
    
    Args:
        system_graph: Dictionary containing the system graph
        
    Returns:
        Dictionary with energy calculation results
    """
    # Calculate total loads
    total_cooling_load = calculate_total_cooling_load(system_graph)
    total_heating_load = calculate_total_heating_load(system_graph)
    
    # Calculate equipment capacities
    cooling_capacity = calculate_cooling_capacity(system_graph)
    heating_capacity = calculate_heating_capacity(system_graph)
    
    # Calculate energy consumption
    annual_cooling_energy = calculate_annual_cooling_energy(system_graph, total_cooling_load)
    annual_heating_energy = calculate_annual_heating_energy(system_graph, total_heating_load)
    annual_fan_energy = calculate_annual_fan_energy(system_graph)
    annual_pump_energy = calculate_annual_pump_energy(system_graph)
    
    # Calculate total energy and costs
    total_annual_energy = annual_cooling_energy + annual_heating_energy + annual_fan_energy + annual_pump_energy
    
    # Assume electricity cost of $0.12/kWh
    electricity_cost = 0.12
    annual_energy_cost = total_annual_energy * electricity_cost
    
    # Calculate efficiency metrics
    cop_cooling = calculate_system_cop_cooling(system_graph)
    cop_heating = calculate_system_cop_heating(system_graph)
    
    return {
        'loads': {
            'total_cooling_load': total_cooling_load,
            'total_heating_load': total_heating_load
        },
        'capacities': {
            'cooling_capacity': cooling_capacity,
            'heating_capacity': heating_capacity,
            'cooling_capacity_ratio': cooling_capacity / total_cooling_load if total_cooling_load > 0 else 0,
            'heating_capacity_ratio': heating_capacity / total_heating_load if total_heating_load > 0 else 0
        },
        'energy': {
            'annual_cooling_energy': annual_cooling_energy,
            'annual_heating_energy': annual_heating_energy,
            'annual_fan_energy': annual_fan_energy,
            'annual_pump_energy': annual_pump_energy,
            'total_annual_energy': total_annual_energy
        },
        'efficiency': {
            'cop_cooling': cop_cooling,
            'cop_heating': cop_heating
        },
        'cost': {
            'annual_energy_cost': annual_energy_cost
        }
    }

def calculate_total_cooling_load(system_graph: Dict) -> float:
    """Calculate total cooling load from zone components"""
    total_load = 0
    
    for node_id, node_data in system_graph['nodes'].items():
        if node_data['type'] == 'zone':
            total_load += node_data['properties'].get('peakCoolingLoad', 0)
    
    return total_load

def calculate_total_heating_load(system_graph: Dict) -> float:
    """Calculate total heating load from zone components"""
    total_load = 0
    
    for node_id, node_data in system_graph['nodes'].items():
        if node_data['type'] == 'zone':
            total_load += node_data['properties'].get('peakHeatingLoad', 0)
    
    return total_load

def calculate_cooling_capacity(system_graph: Dict) -> float:
    """Calculate total cooling capacity from chiller components"""
    total_capacity = 0
    
    for node_id, node_data in system_graph['nodes'].items():
        if node_data['type'] == 'chiller':
            # Convert tons to BTU/h (1 ton = 12,000 BTU/h)
            total_capacity += node_data['properties'].get('capacity', 0) * 12000
    
    return total_capacity

def calculate_heating_capacity(system_graph: Dict) -> float:
    """Calculate total heating capacity from boiler components"""
    total_capacity = 0
    
    for node_id, node_data in system_graph['nodes'].items():
        if node_data['type'] == 'boiler':
            total_capacity += node_data['properties'].get('capacity', 0)
    
    return total_capacity

def calculate_annual_cooling_energy(system_graph: Dict, total_cooling_load: float) -> float:
    """
    Calculate annual cooling energy consumption
    
    This is a simplified calculation. In a real implementation, this would involve
    bin calculations or hourly simulations using weather data.
    """
    # Simplified calculation based on equivalent full load hours
    # Assume 1200 equivalent full load hours for cooling
    equivalent_full_load_hours = 1200
    
    # Calculate average COP of chillers
    total_capacity = 0
    weighted_cop = 0
    
    for node_id, node_data in system_graph['nodes'].items():
        if node_data['type'] == 'chiller':
            capacity = node_data['properties'].get('capacity', 0) * 12000  # Convert tons to BTU/h
            cop = node_data['properties'].get('cop', 3.0)
            
            total_capacity += capacity
            weighted_cop += capacity * cop
    
    if total_capacity > 0:
        average_cop = weighted_cop / total_capacity
    else:
        average_cop = 3.0  # Default value
    
    # Calculate energy in kWh
    # 1 BTU/h = 0.000293071 kW
    cooling_energy_kwh = (total_cooling_load * equivalent_full_load_hours * 0.000293071) / average_cop
    
    return cooling_energy_kwh

def calculate_annual_heating_energy(system_graph: Dict, total_heating_load: float) -> float:
    """
    Calculate annual heating energy consumption
    
    This is a simplified calculation. In a real implementation, this would involve
    bin calculations or hourly simulations using weather data.
    """
    # Simplified calculation based on equivalent full load hours
    # Assume 800 equivalent full load hours for heating
    equivalent_full_load_hours = 800
    
    # Calculate average efficiency of boilers
    total_capacity = 0
    weighted_efficiency = 0
    
    for node_id, node_data in system_graph['nodes'].items():
        if node_data['type'] == 'boiler':
            capacity = node_data['properties'].get('capacity', 0)
            efficiency = node_data['properties'].get('efficiency', 0.8)
            
            total_capacity += capacity
            weighted_efficiency += capacity * efficiency
    
    if total_capacity > 0:
        average_efficiency = weighted_efficiency / total_capacity
    else:
        average_efficiency = 0.8  # Default value
    
    # Calculate energy in kWh
    # 1 BTU/h = 0.000293071 kW
    heating_energy_kwh = (total_heating_load * equivalent_full_load_hours * 0.000293071) / average_efficiency
    
    return heating_energy_kwh

def calculate_annual_fan_energy(system_graph: Dict) -> float:
    """Calculate annual fan energy consumption"""
    total_fan_power = 0
    
    for node_id, node_data in system_graph['nodes'].items():
        if node_data['type'] == 'airHandler':
            # Convert HP to kW (1 HP = 0.7457 kW)
            fan_power_kw = node_data['properties'].get('fanPower', 0) * 0.7457
            total_fan_power += fan_power_kw
    
    # Assume 3000 operating hours per year
    annual_fan_energy = total_fan_power * 3000
    
    return annual_fan_energy

def calculate_annual_pump_energy(system_graph: Dict) -> float:
    """Calculate annual pump energy consumption"""
    total_pump_power = 0
    
    for node_id, node_data in system_graph['nodes'].items():
        if node_data['type'] == 'pump':
            # Convert HP to kW (1 HP = 0.7457 kW)
            pump_power_kw = node_data['properties'].get('power', 0) * 0.7457
            total_pump_power += pump_power_kw
    
    # Assume 3000 operating hours per year
    annual_pump_energy = total_pump_power * 3000
    
    return annual_pump_energy

def calculate_system_cop_cooling(system_graph: Dict) -> float:
    """Calculate system COP for cooling"""
    # This is a simplified calculation
    # In a real implementation, this would be more complex
    
    # Get chiller COPs
    chiller_cops = [
        node_data['properties'].get('cop', 3.0)
        for node_data in system_graph['nodes'].values()
        if node_data['type'] == 'chiller'
    ]
    
    if not chiller_cops:
        return 0
    
    # Calculate average COP
    average_cop = sum(chiller_cops) / len(chiller_cops)
    
    # Apply system factors (pumps, fans, etc.)
    # Assume 15% reduction due to auxiliary equipment
    system_cop = average_cop * 0.85
    
    return system_cop

def calculate_system_cop_heating(system_graph: Dict) -> float:
    """Calculate system COP for heating"""
    # This is a simplified calculation
    # In a real implementation, this would be more complex
    
    # Get boiler efficiencies
    boiler_efficiencies = [
        node_data['properties'].get('efficiency', 0.8)
        for node_data in system_graph['nodes'].values()
        if node_data['type'] == 'boiler'
    ]
    
    if not boiler_efficiencies:
        return 0
    
    # Calculate average efficiency
    average_efficiency = sum(boiler_efficiencies) / len(boiler_efficiencies)
    
    # Convert efficiency to COP (COP = 1/efficiency for combustion systems)
    # For electric systems, COP would be calculated differently
    system_cop = average_efficiency
    
    return system_cop
