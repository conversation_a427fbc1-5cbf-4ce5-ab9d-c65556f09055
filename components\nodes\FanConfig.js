import React from 'react';
import { Position } from 'reactflow';

// Fan:ConstantVolume Node Configuration
export const fanConstantVolumeConfig = {
  componentType: 'OS:Fan:ConstantVolume',
  nodeType: 'fan-constant-volume',
  inputs: [
    {
      id: 'inlet',
      position: Position.Left,
      className: 'air-handle',
      style: { top: '50%' }
    }
  ],
  outputs: [
    {
      id: 'outlet',
      position: Position.Right,
      className: 'air-handle',
      style: { top: '50%' }
    }
  ],
  displayProperties: [
    {
      name: 'fanEfficiency',
      label: 'Fan Efficiency',
      unit: '%',
      format: (value) => (value * 100).toFixed(1)
    },
    {
      name: 'pressureRise',
      label: 'Pressure Rise',
      unit: 'Pa',
      format: (value) => value
    },
    {
      name: 'motorEfficiency',
      label: 'Motor Efficiency',
      unit: '%',
      format: (value) => (value * 100).toFixed(1)
    },
    {
      name: 'maximumFlowRate',
      label: 'Flow Rate',
      unit: 'm³/s',
      format: (value) => value
    }
  ],
  icon: (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="12" cy="12" r="8" stroke="#ff9800" strokeWidth="2"/>
      <path d="M12 4L12 20" stroke="#ff9800" strokeWidth="2"/>
      <path d="M4 12L20 12" stroke="#ff9800" strokeWidth="2"/>
      <path d="M7 7L17 17" stroke="#ff9800" strokeWidth="2"/>
      <path d="M7 17L17 7" stroke="#ff9800" strokeWidth="2"/>
    </svg>
  )
};

// Fan:VariableVolume Node Configuration
export const fanVariableVolumeConfig = {
  componentType: 'OS:Fan:VariableVolume',
  nodeType: 'fan-variable-volume',
  inputs: [
    {
      id: 'inlet',
      position: Position.Left,
      className: 'air-handle',
      style: { top: '50%' }
    }
  ],
  outputs: [
    {
      id: 'outlet',
      position: Position.Right,
      className: 'air-handle',
      style: { top: '50%' }
    }
  ],
  displayProperties: [
    {
      name: 'fanEfficiency',
      label: 'Fan Efficiency',
      unit: '%',
      format: (value) => (value * 100).toFixed(1)
    },
    {
      name: 'pressureRise',
      label: 'Pressure Rise',
      unit: 'Pa',
      format: (value) => value
    },
    {
      name: 'motorEfficiency',
      label: 'Motor Efficiency',
      unit: '%',
      format: (value) => (value * 100).toFixed(1)
    },
    {
      name: 'maximumFlowRate',
      label: 'Flow Rate',
      unit: 'm³/s',
      format: (value) => value
    }
  ],
  icon: (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="12" cy="12" r="8" stroke="#f57c00" strokeWidth="2"/>
      <path d="M12 4L12 20" stroke="#f57c00" strokeWidth="2"/>
      <path d="M4 12L20 12" stroke="#f57c00" strokeWidth="2"/>
      <path d="M7 7L17 17" stroke="#f57c00" strokeWidth="2"/>
      <path d="M7 17L17 7" stroke="#f57c00" strokeWidth="2"/>
      <path d="M12 8L16 12L12 16L8 12L12 8Z" fill="#f57c00"/>
    </svg>
  )
};

export default {
  fanConstantVolumeConfig,
  fanVariableVolumeConfig
};
