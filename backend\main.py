from fastapi import <PERSON><PERSON><PERSON>, Query, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import os
import re
from typing import Dict, List, Any, Optional
import json

# Create FastAPI app
app = FastAPI(
    title="HVAC Designer API",
    description="API for HVAC Designer application",
    version="1.0.0"
)

# Add CORS middleware to allow requests from frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # For development, allow all origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# IDD file path - relative to this file
IDD_FILE_PATH = os.path.join(os.path.dirname(__file__), 'data', 'OpenStudio.idd')

# Cache for parsed IDD data
idd_cache = None

@app.get("/")
async def root():
    """Root endpoint to check if API is running"""
    return {"message": "HVAC Designer API is running"}

@app.get("/idd")
async def get_idd(type: Optional[str] = Query(None, description="Filter by component type")):
    """Get IDD data, optionally filtered by component type"""
    # Get IDD data
    idd_data = get_idd_data()

    # Filter by component type if specified
    if type:
        if type in idd_data['components']:
            result = {
                'components': {
                    type: idd_data['components'][type]
                }
            }

            # Include reference lists if available
            if 'reference_lists' in idd_data:
                result['reference_lists'] = idd_data['reference_lists']
        else:
            result = {
                'components': {}
            }
    else:
        result = idd_data

    return result

@app.get("/idd/components")
async def list_components():
    """List all available component types"""
    # Get IDD data
    idd_data = get_idd_data()

    # Extract component names, groups, and references
    components = []
    for name, component in idd_data['components'].items():
        component_info = {
            'name': name,
            'group': component.get('group', '')
        }

        # Add references if available
        if 'references' in component:
            component_info['references'] = component['references']

        components.append(component_info)

    # Sort by group then name
    components.sort(key=lambda x: (x['group'], x['name']))

    result = {
        'success': True,
        'components': components
    }

    # Include reference lists if available
    if 'reference_lists' in idd_data:
        result['reference_lists'] = idd_data['reference_lists']

    return result

def get_idd_data() -> Dict:
    """
    Get parsed IDD data, using cache if available

    Returns:
        Dictionary containing parsed IDD data
    """
    global idd_cache

    # Use the actual IDD file parser
    if idd_cache is None:
        print("Parsing IDD file...")
        idd_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data', 'OpenStudio.idd')

        if os.path.exists(idd_file_path):
            try:
                idd_cache = parse_idd_file(idd_file_path)
                print(f"Successfully parsed IDD file: {idd_file_path}")
                print(f"Found {len(idd_cache['components'])} components and {len(idd_cache['reference_lists'])} reference lists")
            except Exception as e:
                print(f"Error parsing IDD file: {e}")
                print("Falling back to mock data")
                idd_cache = get_mock_idd_data()
        else:
            print(f"IDD file not found at {idd_file_path}")
            print("Falling back to mock data")
            idd_cache = get_mock_idd_data()

    return idd_cache

def parse_idd_file(file_path: str) -> Dict:
    """
    Parse an IDD file and extract component definitions

    Args:
        file_path: Path to the IDD file

    Returns:
        Dictionary containing parsed IDD data
    """
    components = {}
    current_component = None
    current_field = None
    current_group = ""

    # Dictionary to store reference lists
    reference_lists = {}

    # Regular expressions for parsing
    component_re = re.compile(r'^\s*\\group\s+(.+)$|^\s*([A-Za-z0-9:_]+),\s*$')
    field_re = re.compile(r'^\s*([A-Za-z0-9:_]+)\s*;\s*$')
    field_attr_re = re.compile(r'^\s*\\([\w-]+)(?:\s+(.+))?$')
    field_code_re = re.compile(r'^\s*([A-Z][0-9]+),\s*\\field\s+(.+)$')

    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            for line in f:
                line = line.strip()

                # Skip empty lines and comments
                if not line or line.startswith('!'):
                    continue

                # Check if this is a new component
                component_match = component_re.match(line)
                if component_match:
                    group_name = component_match.group(1)
                    component_name = component_match.group(2)

                    if group_name:
                        # Update current group
                        current_group = group_name
                        continue

                    if component_name:
                        # Start a new component
                        current_component = {
                            'name': component_name,
                            'group': current_group,
                            'fields': {},
                            'references': []  # Add a list to store references defined by this component
                        }
                        components[component_name] = current_component
                        current_field = None
                    continue

                # Check if this is a field code and name
                field_code_match = field_code_re.match(line)
                if field_code_match and current_component:
                    field_code = field_code_match.group(1)
                    field_name = field_code_match.group(2)

                    current_field = {
                        'name': field_name,
                        'type': 'string',  # Default type
                        'required': False,
                        'default': None,
                        'units': None,
                        'note': None,
                        'options': [],
                        'reference': None,       # Add reference field
                        'object-list': None      # Add object-list field
                    }
                    current_component['fields'][field_name] = current_field
                    continue

                # Check if this is a field attribute
                attr_match = field_attr_re.match(line)
                if attr_match and current_field:
                    attr_name = attr_match.group(1)
                    attr_value = attr_match.group(2)

                    if attr_name == 'required-field':
                        current_field['required'] = True
                    elif attr_name == 'type':
                        current_field['type'] = attr_value
                    elif attr_name == 'units':
                        current_field['units'] = attr_value
                    elif attr_name == 'default':
                        current_field['default'] = attr_value
                    elif attr_name == 'note':
                        current_field['note'] = attr_value
                    elif attr_name == 'key':
                        current_field['options'].append(attr_value)
                    elif attr_name == 'reference':
                        # Store the reference list name
                        current_field['reference'] = attr_value

                        # Add this component to the reference list
                        if attr_value not in reference_lists:
                            reference_lists[attr_value] = []

                        # Add the component name to the reference list
                        if current_component['name'] not in reference_lists[attr_value]:
                            reference_lists[attr_value].append(current_component['name'])

                        # Add the reference to the component's references list
                        if attr_value not in current_component['references']:
                            current_component['references'].append(attr_value)

                    elif attr_name == 'object-list':
                        # Store the object-list name
                        current_field['object-list'] = attr_value
                    elif attr_name == 'field' and current_field is None and current_component:
                        # This is a field definition without a code
                        field_name = attr_value
                        current_field = {
                            'name': field_name,
                            'type': 'string',  # Default type
                            'required': False,
                            'default': None,
                            'units': None,
                            'note': None,
                            'options': [],
                            'reference': None,       # Add reference field
                            'object-list': None      # Add object-list field
                        }
                        current_component['fields'][field_name] = current_field

                # Check if this is a new field (ending with semicolon)
                field_match = field_re.match(line)
                if field_match and current_component:
                    # End of current field, reset for next field
                    current_field = None
                    continue

        # Second pass to resolve object-list references
        for component_name, component in components.items():
            for field_name, field in component['fields'].items():
                if field['object-list']:
                    object_list_name = field['object-list']

                    # If this object-list exists in our reference_lists, add the referenced components
                    if object_list_name in reference_lists:
                        field['options'] = reference_lists[object_list_name]

        # Filter to only include HVAC components
        hvac_components = {}
        hvac_groups = ['HVAC', 'OpenStudio HVAC', 'HVAC Templates', 'HVAC Design Objects', 'Node-Branch Management']

        for name, component in components.items():
            if component['group'] in hvac_groups or name.startswith('OS:'):
                hvac_components[name] = component

        print(f"Parsed {len(components)} components, {len(hvac_components)} HVAC components")

        # If we don't have any fields for OS:AirLoopHVAC, use mock data
        if 'OS:AirLoopHVAC' in hvac_components and not hvac_components['OS:AirLoopHVAC']['fields']:
            print("No fields found for OS:AirLoopHVAC, using mock data")
            hvac_components['OS:AirLoopHVAC'] = get_mock_idd_data()['components']['OS:AirLoopHVAC']

        return {
            'components': hvac_components,
            'reference_lists': reference_lists
        }
    except Exception as e:
        print(f"Error parsing IDD file: {e}")
        print("Falling back to mock data")
        return get_mock_idd_data()

def get_mock_idd_data() -> Dict:
    """
    Get mock IDD data for testing

    Returns:
        Dictionary containing mock IDD data
    """
    return {
        'components': {
            'OS:AirLoopHVAC': {
                'name': 'OS:AirLoopHVAC',
                'group': 'HVAC',
                'fields': {
                    'Name': {
                        'name': 'Name',
                        'type': 'string',
                        'required': True,
                        'default': None,
                        'units': None,
                        'note': None,
                        'options': []
                    },
                    'DesignSupplyAirFlowRate': {
                        'name': 'DesignSupplyAirFlowRate',
                        'type': 'real',
                        'required': False,
                        'default': 'autosize',
                        'units': 'm3/s',
                        'note': None,
                        'options': []
                    },
                    'AvailabilitySchedule': {
                        'name': 'AvailabilitySchedule',
                        'type': 'object-list',
                        'required': False,
                        'default': None,
                        'units': None,
                        'note': None,
                        'options': ['OS:Schedule:Constant', 'OS:Schedule:Compact']
                    },
                    'NightCycleControlType': {
                        'name': 'NightCycleControlType',
                        'type': 'choice',
                        'required': False,
                        'default': 'StayOff',
                        'units': None,
                        'note': None,
                        'options': ['StayOff', 'CycleOnAny', 'CycleOnControlZone']
                    }
                }
            },
            'OS:AirLoopHVAC:UnitarySystem': {
                'name': 'OS:AirLoopHVAC:UnitarySystem',
                'group': 'HVAC',
                'fields': {
                    'Name': {
                        'name': 'Name',
                        'type': 'string',
                        'required': True,
                        'default': None,
                        'units': None,
                        'note': 'Unique name for the unitary system',
                        'options': []
                    },
                    'ControlType': {
                        'name': 'ControlType',
                        'type': 'choice',
                        'required': True,
                        'default': 'Load',
                        'units': None,
                        'note': 'How the system will be controlled',
                        'options': ['Load', 'SetPoint', 'SingleZoneVAV']
                    },
                    'ControllingZone': {
                        'name': 'ControllingZone',
                        'type': 'object-list',
                        'required': False,
                        'default': None,
                        'units': None,
                        'note': 'Zone name where thermostat is located',
                        'object-list': 'ThermalZones',
                        'options': ['OS:ThermalZone']
                    },
                    'DehumidificationControlType': {
                        'name': 'DehumidificationControlType',
                        'type': 'choice',
                        'required': False,
                        'default': 'None',
                        'units': None,
                        'note': 'Type of dehumidification control',
                        'options': ['None', 'CoolReheat']
                    },
                    'AvailabilitySchedule': {
                        'name': 'AvailabilitySchedule',
                        'type': 'object-list',
                        'required': False,
                        'default': None,
                        'units': None,
                        'note': 'Schedule to control when system is available',
                        'object-list': 'ScheduleNames',
                        'options': ['OS:Schedule:Constant', 'OS:Schedule:Compact', 'OS:Schedule:Ruleset']
                    },
                    'SupplyFan': {
                        'name': 'SupplyFan',
                        'type': 'object-list',
                        'required': False,
                        'default': None,
                        'units': None,
                        'note': 'Supply air fan',
                        'object-list': 'FanNames',
                        'options': ['OS:Fan:ConstantVolume', 'OS:Fan:VariableVolume', 'OS:Fan:OnOff', 'OS:Fan:SystemModel']
                    },
                    'FanPlacement': {
                        'name': 'FanPlacement',
                        'type': 'choice',
                        'required': False,
                        'default': 'BlowThrough',
                        'units': None,
                        'note': 'Fan placement in the air path',
                        'options': ['BlowThrough', 'DrawThrough']
                    },
                    'SupplyAirFanOperatingModeSchedule': {
                        'name': 'SupplyAirFanOperatingModeSchedule',
                        'type': 'object-list',
                        'required': False,
                        'default': None,
                        'units': None,
                        'note': 'Schedule to control fan operation mode',
                        'object-list': 'ScheduleNames',
                        'options': ['OS:Schedule:Constant', 'OS:Schedule:Compact', 'OS:Schedule:Ruleset']
                    },
                    'HeatingCoil': {
                        'name': 'HeatingCoil',
                        'type': 'object-list',
                        'required': False,
                        'default': None,
                        'units': None,
                        'note': 'Heating coil',
                        'object-list': 'HeatingCoilNames',
                        'options': ['OS:Coil:Heating:Water', 'OS:Coil:Heating:Gas', 'OS:Coil:Heating:Electric', 'OS:Coil:Heating:DX:SingleSpeed']
                    },
                    'DXHeatingCoilSizingRatio': {
                        'name': 'DXHeatingCoilSizingRatio',
                        'type': 'real',
                        'required': False,
                        'default': 1.0,
                        'units': None,
                        'note': 'Used for sizing when heating coil is DX',
                        'options': []
                    },
                    'CoolingCoil': {
                        'name': 'CoolingCoil',
                        'type': 'object-list',
                        'required': False,
                        'default': None,
                        'units': None,
                        'note': 'Cooling coil',
                        'object-list': 'CoolingCoilNames',
                        'options': ['OS:Coil:Cooling:Water', 'OS:Coil:Cooling:DX', 'OS:Coil:Cooling:DX:SingleSpeed', 'OS:Coil:Cooling:DX:TwoSpeed']
                    },
                    'UseDOASDXCoolingCoil': {
                        'name': 'UseDOASDXCoolingCoil',
                        'type': 'choice',
                        'required': False,
                        'default': 'No',
                        'units': None,
                        'note': 'Use DOAS DX cooling coil',
                        'options': ['Yes', 'No']
                    },
                    'DOASDXCoolingCoilLeavingMinimumAirTemperature': {
                        'name': 'DOASDXCoolingCoilLeavingMinimumAirTemperature',
                        'type': 'real',
                        'required': False,
                        'default': 2.0,
                        'units': 'C',
                        'note': 'Minimum air temperature leaving the cooling coil',
                        'options': []
                    },
                    'LatentLoadControl': {
                        'name': 'LatentLoadControl',
                        'type': 'choice',
                        'required': False,
                        'default': 'SensibleOnlyLoadControl',
                        'units': None,
                        'note': 'How latent load is controlled',
                        'options': ['SensibleOnlyLoadControl', 'LatentOnlyLoadControl', 'LatentWithSensibleLoadControl', 'LatentOrSensibleLoadControl']
                    },
                    'SupplementalHeatingCoil': {
                        'name': 'SupplementalHeatingCoil',
                        'type': 'object-list',
                        'required': False,
                        'default': None,
                        'units': None,
                        'note': 'Supplemental heating coil',
                        'object-list': 'HeatingCoilNames',
                        'options': ['OS:Coil:Heating:Water', 'OS:Coil:Heating:Gas', 'OS:Coil:Heating:Electric']
                    },
                    'SupplyAirFlowRateMethod': {
                        'name': 'SupplyAirFlowRateMethod',
                        'type': 'choice',
                        'required': False,
                        'default': 'SupplyAirFlowRate',
                        'units': None,
                        'note': 'Method for determining supply air flow rate',
                        'options': ['SupplyAirFlowRate', 'FlowPerFloorArea', 'FlowPerCoolingCapacity', 'FlowPerHeatingCapacity']
                    },
                    'SupplyAirFlowRate': {
                        'name': 'SupplyAirFlowRate',
                        'type': 'real',
                        'required': False,
                        'default': 'autosize',
                        'units': 'm3/s',
                        'note': 'Supply air flow rate when SupplyAirFlowRateMethod = SupplyAirFlowRate',
                        'options': []
                    },
                    'SupplyAirFlowRatePerFloorArea': {
                        'name': 'SupplyAirFlowRatePerFloorArea',
                        'type': 'real',
                        'required': False,
                        'default': 'autosize',
                        'units': 'm3/s-m2',
                        'note': 'Supply air flow rate per floor area when SupplyAirFlowRateMethod = FlowPerFloorArea',
                        'options': []
                    },
                    'SupplyAirFlowRatePerCoolingCapacity': {
                        'name': 'SupplyAirFlowRatePerCoolingCapacity',
                        'type': 'real',
                        'required': False,
                        'default': 'autosize',
                        'units': 'm3/s-W',
                        'note': 'Supply air flow rate per cooling capacity when SupplyAirFlowRateMethod = FlowPerCoolingCapacity',
                        'options': []
                    },
                    'SupplyAirFlowRatePerHeatingCapacity': {
                        'name': 'SupplyAirFlowRatePerHeatingCapacity',
                        'type': 'real',
                        'required': False,
                        'default': 'autosize',
                        'units': 'm3/s-W',
                        'note': 'Supply air flow rate per heating capacity when SupplyAirFlowRateMethod = FlowPerHeatingCapacity',
                        'options': []
                    },
                    'MaximumSupplyAirTemperature': {
                        'name': 'MaximumSupplyAirTemperature',
                        'type': 'real',
                        'required': False,
                        'default': 'autosize',
                        'units': 'C',
                        'note': 'Maximum supply air temperature',
                        'options': []
                    },
                    'MaximumOutdoorDryBulbTemperatureForSupplementalHeaterOperation': {
                        'name': 'MaximumOutdoorDryBulbTemperatureForSupplementalHeaterOperation',
                        'type': 'real',
                        'required': False,
                        'default': 21.0,
                        'units': 'C',
                        'note': 'Maximum outdoor temperature for supplemental heater operation',
                        'options': []
                    },
                    'OutdoorDryBulbTemperatureSensorNode': {
                        'name': 'OutdoorDryBulbTemperatureSensorNode',
                        'type': 'string',
                        'required': False,
                        'default': None,
                        'units': None,
                        'note': 'Node name for outdoor dry-bulb temperature sensor',
                        'options': []
                    },
                    'MaximumCyclingRate': {
                        'name': 'MaximumCyclingRate',
                        'type': 'real',
                        'required': False,
                        'default': 2.5,
                        'units': 'cycles/hr',
                        'note': 'Maximum cycling rate',
                        'options': []
                    },
                    'HeatPumpTimeConstant': {
                        'name': 'HeatPumpTimeConstant',
                        'type': 'real',
                        'required': False,
                        'default': 60.0,
                        'units': 's',
                        'note': 'Heat pump time constant',
                        'options': []
                    },
                    'FractionOfOnCyclePowerUse': {
                        'name': 'FractionOfOnCyclePowerUse',
                        'type': 'real',
                        'required': False,
                        'default': 0.01,
                        'units': None,
                        'note': 'Fraction of on-cycle power use',
                        'options': []
                    },
                    'HeatPumpFanDelayTime': {
                        'name': 'HeatPumpFanDelayTime',
                        'type': 'real',
                        'required': False,
                        'default': 60.0,
                        'units': 's',
                        'note': 'Heat pump fan delay time',
                        'options': []
                    },
                    'AncilliaryOnCycleElectricPower': {
                        'name': 'AncilliaryOnCycleElectricPower',
                        'type': 'real',
                        'required': False,
                        'default': 0.0,
                        'units': 'W',
                        'note': 'Ancillary on-cycle electric power',
                        'options': []
                    },
                    'AncilliaryOffCycleElectricPower': {
                        'name': 'AncilliaryOffCycleElectricPower',
                        'type': 'real',
                        'required': False,
                        'default': 0.0,
                        'units': 'W',
                        'note': 'Ancillary off-cycle electric power',
                        'options': []
                    },
                    'DesignHeatRecoveryWaterFlowRate': {
                        'name': 'DesignHeatRecoveryWaterFlowRate',
                        'type': 'real',
                        'required': False,
                        'default': 0.0,
                        'units': 'm3/s',
                        'note': 'Design heat recovery water flow rate',
                        'options': []
                    },
                    'MaximumTemperatureForHeatRecovery': {
                        'name': 'MaximumTemperatureForHeatRecovery',
                        'type': 'real',
                        'required': False,
                        'default': 80.0,
                        'units': 'C',
                        'note': 'Maximum temperature for heat recovery',
                        'options': []
                    },
                    'DesignSpecificationMultispeedObject': {
                        'name': 'DesignSpecificationMultispeedObject',
                        'type': 'object-list',
                        'required': False,
                        'default': None,
                        'units': None,
                        'note': 'Design specification for multispeed operation',
                        'options': ['OS:UnitarySystemPerformance:Multispeed']
                    }
                }
            },
            'OS:Chiller:Electric:EIR': {
                'name': 'OS:Chiller:Electric:EIR',
                'group': 'HVAC',
                'fields': {
                    'Name': {
                        'name': 'Name',
                        'type': 'string',
                        'required': True,
                        'default': None,
                        'units': None,
                        'note': None,
                        'options': []
                    },
                    'ReferenceCapacity': {
                        'name': 'ReferenceCapacity',
                        'type': 'real',
                        'required': False,
                        'default': 'autosize',
                        'units': 'W',
                        'note': None,
                        'options': []
                    },
                    'ReferenceCOP': {
                        'name': 'ReferenceCOP',
                        'type': 'real',
                        'required': True,
                        'default': None,
                        'units': 'W/W',
                        'note': None,
                        'options': []
                    },
                    'MinimumPartLoadRatio': {
                        'name': 'MinimumPartLoadRatio',
                        'type': 'real',
                        'required': False,
                        'default': 0.1,
                        'units': None,
                        'note': None,
                        'options': []
                    },
                    'MaximumPartLoadRatio': {
                        'name': 'MaximumPartLoadRatio',
                        'type': 'real',
                        'required': False,
                        'default': 1.0,
                        'units': None,
                        'note': None,
                        'options': []
                    },
                    'OptimumPartLoadRatio': {
                        'name': 'OptimumPartLoadRatio',
                        'type': 'real',
                        'required': False,
                        'default': 1.0,
                        'units': None,
                        'note': None,
                        'options': []
                    },
                    'CondenserType': {
                        'name': 'CondenserType',
                        'type': 'choice',
                        'required': False,
                        'default': 'WaterCooled',
                        'units': None,
                        'note': None,
                        'options': ['AirCooled', 'WaterCooled', 'EvaporativelyCooled']
                    }
                }
            },
            'OS:Boiler:HotWater': {
                'name': 'OS:Boiler:HotWater',
                'group': 'HVAC',
                'fields': {
                    'Name': {
                        'name': 'Name',
                        'type': 'string',
                        'required': True,
                        'default': None,
                        'units': None,
                        'note': None,
                        'options': []
                    },
                    'NominalCapacity': {
                        'name': 'NominalCapacity',
                        'type': 'real',
                        'required': False,
                        'default': 'autosize',
                        'units': 'W',
                        'note': None,
                        'options': []
                    },
                    'FuelType': {
                        'name': 'FuelType',
                        'type': 'choice',
                        'required': True,
                        'default': 'NaturalGas',
                        'units': None,
                        'note': None,
                        'options': ['NaturalGas', 'Electricity', 'PropaneGas', 'FuelOil#1', 'FuelOil#2', 'Coal', 'Diesel', 'Gasoline', 'OtherFuel1', 'OtherFuel2']
                    },
                    'NominalThermalEfficiency': {
                        'name': 'NominalThermalEfficiency',
                        'type': 'real',
                        'required': False,
                        'default': 0.8,
                        'units': None,
                        'note': None,
                        'options': []
                    },
                    'DesignWaterOutletTemperature': {
                        'name': 'DesignWaterOutletTemperature',
                        'type': 'real',
                        'required': False,
                        'default': 82.0,
                        'units': 'C',
                        'note': None,
                        'options': []
                    },
                    'MaximumPartLoadRatio': {
                        'name': 'MaximumPartLoadRatio',
                        'type': 'real',
                        'required': False,
                        'default': 1.0,
                        'units': None,
                        'note': None,
                        'options': []
                    }
                }
            },
            'OS:Fan:ConstantVolume': {
                'name': 'OS:Fan:ConstantVolume',
                'group': 'HVAC',
                'fields': {
                    'Name': {
                        'name': 'Name',
                        'type': 'string',
                        'required': True,
                        'default': None,
                        'units': None,
                        'note': None,
                        'options': []
                    },
                    'FanEfficiency': {
                        'name': 'FanEfficiency',
                        'type': 'real',
                        'required': True,
                        'default': 0.7,
                        'units': None,
                        'note': None,
                        'options': []
                    },
                    'PressureRise': {
                        'name': 'PressureRise',
                        'type': 'real',
                        'required': True,
                        'default': 500,
                        'units': 'Pa',
                        'note': None,
                        'options': []
                    },
                    'MotorEfficiency': {
                        'name': 'MotorEfficiency',
                        'type': 'real',
                        'required': True,
                        'default': 0.9,
                        'units': None,
                        'note': None,
                        'options': []
                    },
                    'MaximumFlowRate': {
                        'name': 'MaximumFlowRate',
                        'type': 'real',
                        'required': False,
                        'default': 'autosize',
                        'units': 'm3/s',
                        'note': None,
                        'options': []
                    }
                }
            },
            'OS:Fan:VariableVolume': {
                'name': 'OS:Fan:VariableVolume',
                'group': 'HVAC',
                'fields': {
                    'Name': {
                        'name': 'Name',
                        'type': 'string',
                        'required': True,
                        'default': None,
                        'units': None,
                        'note': None,
                        'options': []
                    },
                    'FanEfficiency': {
                        'name': 'FanEfficiency',
                        'type': 'real',
                        'required': True,
                        'default': 0.7,
                        'units': None,
                        'note': None,
                        'options': []
                    },
                    'PressureRise': {
                        'name': 'PressureRise',
                        'type': 'real',
                        'required': True,
                        'default': 500,
                        'units': 'Pa',
                        'note': None,
                        'options': []
                    },
                    'MotorEfficiency': {
                        'name': 'MotorEfficiency',
                        'type': 'real',
                        'required': True,
                        'default': 0.9,
                        'units': None,
                        'note': None,
                        'options': []
                    },
                    'MaximumFlowRate': {
                        'name': 'MaximumFlowRate',
                        'type': 'real',
                        'required': False,
                        'default': 'autosize',
                        'units': 'm3/s',
                        'note': None,
                        'options': []
                    },
                    'FanPowerMinimumFlowRateInputMethod': {
                        'name': 'FanPowerMinimumFlowRateInputMethod',
                        'type': 'choice',
                        'required': False,
                        'default': 'Fraction',
                        'units': None,
                        'note': None,
                        'options': ['Fraction', 'FixedFlowRate']
                    },
                    'FanPowerMinimumFlowFraction': {
                        'name': 'FanPowerMinimumFlowFraction',
                        'type': 'real',
                        'required': False,
                        'default': 0.25,
                        'units': None,
                        'note': None,
                        'options': []
                    }
                }
            },
            'OS:Coil:Cooling:Water': {
                'name': 'OS:Coil:Cooling:Water',
                'group': 'HVAC',
                'fields': {
                    'Name': {
                        'name': 'Name',
                        'type': 'string',
                        'required': True,
                        'default': None,
                        'units': None,
                        'note': None,
                        'options': []
                    },
                    'DesignWaterFlowRate': {
                        'name': 'DesignWaterFlowRate',
                        'type': 'real',
                        'required': False,
                        'default': 'autosize',
                        'units': 'm3/s',
                        'note': None,
                        'options': []
                    },
                    'DesignAirFlowRate': {
                        'name': 'DesignAirFlowRate',
                        'type': 'real',
                        'required': False,
                        'default': 'autosize',
                        'units': 'm3/s',
                        'note': None,
                        'options': []
                    },
                    'DesignInletWaterTemperature': {
                        'name': 'DesignInletWaterTemperature',
                        'type': 'real',
                        'required': False,
                        'default': 7.0,
                        'units': 'C',
                        'note': None,
                        'options': []
                    },
                    'DesignInletAirTemperature': {
                        'name': 'DesignInletAirTemperature',
                        'type': 'real',
                        'required': False,
                        'default': 25.0,
                        'units': 'C',
                        'note': None,
                        'options': []
                    },
                    'DesignOutletAirTemperature': {
                        'name': 'DesignOutletAirTemperature',
                        'type': 'real',
                        'required': False,
                        'default': 10.0,
                        'units': 'C',
                        'note': None,
                        'options': []
                    }
                }
            },
            'OS:Coil:Heating:Water': {
                'name': 'OS:Coil:Heating:Water',
                'group': 'HVAC',
                'fields': {
                    'Name': {
                        'name': 'Name',
                        'type': 'string',
                        'required': True,
                        'default': None,
                        'units': None,
                        'note': None,
                        'options': []
                    },
                    'DesignWaterFlowRate': {
                        'name': 'DesignWaterFlowRate',
                        'type': 'real',
                        'required': False,
                        'default': 'autosize',
                        'units': 'm3/s',
                        'note': None,
                        'options': []
                    },
                    'DesignAirFlowRate': {
                        'name': 'DesignAirFlowRate',
                        'type': 'real',
                        'required': False,
                        'default': 'autosize',
                        'units': 'm3/s',
                        'note': None,
                        'options': []
                    },
                    'DesignInletWaterTemperature': {
                        'name': 'DesignInletWaterTemperature',
                        'type': 'real',
                        'required': False,
                        'default': 60.0,
                        'units': 'C',
                        'note': None,
                        'options': []
                    },
                    'DesignInletAirTemperature': {
                        'name': 'DesignInletAirTemperature',
                        'type': 'real',
                        'required': False,
                        'default': 10.0,
                        'units': 'C',
                        'note': None,
                        'options': []
                    },
                    'DesignOutletAirTemperature': {
                        'name': 'DesignOutletAirTemperature',
                        'type': 'real',
                        'required': False,
                        'default': 40.0,
                        'units': 'C',
                        'note': None,
                        'options': []
                    }
                }
            },
            'OS:PlantLoop': {
                'name': 'OS:PlantLoop',
                'group': 'HVAC',
                'fields': {
                    'Name': {
                        'name': 'Name',
                        'type': 'string',
                        'required': True,
                        'default': None,
                        'units': None,
                        'note': None,
                        'options': []
                    },
                    'FluidType': {
                        'name': 'FluidType',
                        'type': 'choice',
                        'required': False,
                        'default': 'Water',
                        'units': None,
                        'note': None,
                        'options': ['Water', 'Steam', 'UserDefinedFluidType']
                    },
                    'LoopTemperatureSetpointNodeName': {
                        'name': 'LoopTemperatureSetpointNodeName',
                        'type': 'node',
                        'required': False,
                        'default': None,
                        'units': None,
                        'note': None,
                        'options': []
                    },
                    'MaximumLoopTemperature': {
                        'name': 'MaximumLoopTemperature',
                        'type': 'real',
                        'required': False,
                        'default': 82.0,
                        'units': 'C',
                        'note': None,
                        'options': []
                    },
                    'MinimumLoopTemperature': {
                        'name': 'MinimumLoopTemperature',
                        'type': 'real',
                        'required': False,
                        'default': 5.0,
                        'units': 'C',
                        'note': None,
                        'options': []
                    },
                    'MaximumLoopFlowRate': {
                        'name': 'MaximumLoopFlowRate',
                        'type': 'real',
                        'required': False,
                        'default': 'autosize',
                        'units': 'm3/s',
                        'note': None,
                        'options': []
                    },
                    'MinimumLoopFlowRate': {
                        'name': 'MinimumLoopFlowRate',
                        'type': 'real',
                        'required': False,
                        'default': 0.0,
                        'units': 'm3/s',
                        'note': None,
                        'options': []
                    },
                    'LoadDistributionScheme': {
                        'name': 'LoadDistributionScheme',
                        'type': 'choice',
                        'required': False,
                        'default': 'SequentialLoad',
                        'units': None,
                        'note': None,
                        'options': ['Optimal', 'SequentialLoad', 'UniformLoad', 'UniformPLR', 'SequentialUniformPLR']
                    }
                }
            }
        }
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
