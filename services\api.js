import axios from 'axios';

// Base URL for API
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

// Create axios instance
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

/**
 * Save an HVAC system design
 * @param {Object} system - The system to save
 * @returns {Promise<Object>} - The saved system
 */
export const saveSystemAPI = async (system) => {
  try {
    const response = await api.post('/systems', system);
    return response.data;
  } catch (error) {
    console.error('Error saving system:', error);
    throw error;
  }
};

/**
 * Get an HVAC system design by ID
 * @param {string} id - The system ID
 * @returns {Promise<Object>} - The system
 */
export const getSystemAPI = async (id) => {
  try {
    const response = await api.get(`/systems/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error getting system:', error);
    throw error;
  }
};

/**
 * List all HVAC system designs
 * @returns {Promise<Array>} - The list of systems
 */
export const listSystemsAPI = async () => {
  try {
    const response = await api.get('/systems');
    return response.data;
  } catch (error) {
    console.error('Error listing systems:', error);
    throw error;
  }
};

/**
 * Delete an HVAC system design by ID
 * @param {string} id - The system ID
 * @returns {Promise<Object>} - The result
 */
export const deleteSystemAPI = async (id) => {
  try {
    const response = await api.delete(`/systems/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error deleting system:', error);
    throw error;
  }
};

/**
 * Calculate energy performance for an HVAC system
 * @param {Object} system - The system to calculate
 * @returns {Promise<Object>} - The calculation results
 */
export const calculateEnergyAPI = async (system) => {
  try {
    const response = await api.post('/calculate/energy', system);
    return response.data;
  } catch (error) {
    console.error('Error calculating energy:', error);
    throw error;
  }
};

/**
 * Save component properties
 * @param {string} nodeId - The node ID
 * @param {Object} properties - The properties to save
 * @returns {Promise<Object>} - The result
 */
export const saveComponentPropertiesAPI = async (nodeId, properties) => {
  try {
    const response = await api.post(`/components/${nodeId}/properties`, { properties });
    return response.data;
  } catch (error) {
    console.error('Error saving component properties:', error);
    throw error;
  }
};
