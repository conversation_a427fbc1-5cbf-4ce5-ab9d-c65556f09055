import React from 'react';
import { Position } from 'reactflow';

// Connector Mixer Node Configuration (for PlantLoop)
export const connectorMixerConfig = {
  componentType: 'OS:Connector:Mixer',
  nodeType: 'connector-mixer',
  inputs: [
    {
      id: 'inlet-1',
      position: Position.Left,
      className: 'water-handle',
      style: { top: '30%' }
    },
    {
      id: 'inlet-2',
      position: Position.Left,
      className: 'water-handle',
      style: { top: '70%' }
    },
    {
      id: 'inlet-3',
      position: Position.Bottom,
      className: 'water-handle',
      style: { left: '30%' }
    }
  ],
  outputs: [
    {
      id: 'outlet',
      position: Position.Right,
      className: 'water-handle',
      style: { top: '50%' }
    }
  ],
  displayProperties: [
    {
      name: 'description',
      label: 'Description',
      unit: ''
    }
  ],
  icon: (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M4 6L10 12" stroke="#9575cd" strokeWidth="2"/>
      <path d="M4 18L10 12" stroke="#9575cd" strokeWidth="2"/>
      <path d="M10 12L20 12" stroke="#9575cd" strokeWidth="2"/>
      <circle cx="10" cy="12" r="2" fill="#9575cd"/>
    </svg>
  )
};

// Zone Mixer Node Configuration (for AirLoopHVAC)
export const zoneMixerConfig = {
  componentType: 'OS:AirLoopHVAC:ZoneMixer',
  nodeType: 'zone-mixer',
  inputs: [
    {
      id: 'inlet-1',
      position: Position.Left,
      className: 'air-handle',
      style: { top: '30%' }
    },
    {
      id: 'inlet-2',
      position: Position.Left,
      className: 'air-handle',
      style: { top: '70%' }
    },
    {
      id: 'inlet-3',
      position: Position.Bottom,
      className: 'air-handle',
      style: { left: '30%' }
    }
  ],
  outputs: [
    {
      id: 'outlet',
      position: Position.Right,
      className: 'air-handle',
      style: { top: '50%' }
    }
  ],
  displayProperties: [
    {
      name: 'description',
      label: 'Description',
      unit: ''
    }
  ],
  icon: (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M4 6L10 12" stroke="#64b5f6" strokeWidth="2"/>
      <path d="M4 18L10 12" stroke="#64b5f6" strokeWidth="2"/>
      <path d="M10 12L20 12" stroke="#64b5f6" strokeWidth="2"/>
      <circle cx="10" cy="12" r="2" fill="#64b5f6"/>
    </svg>
  )
};

// Supply Side Mixer Node Configuration (for AirLoopHVAC)
export const supplySideMixerConfig = {
  componentType: 'OS:Connector:Mixer',
  nodeType: 'supply-side-mixer',
  inputs: [
    {
      id: 'inlet-1',
      position: Position.Left,
      className: 'air-handle',
      style: { top: '30%' }
    },
    {
      id: 'inlet-2',
      position: Position.Left,
      className: 'air-handle',
      style: { top: '70%' }
    }
  ],
  outputs: [
    {
      id: 'outlet',
      position: Position.Right,
      className: 'air-handle',
      style: { top: '50%' }
    }
  ],
  displayProperties: [
    {
      name: 'description',
      label: 'Description',
      unit: ''
    }
  ],
  icon: (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M4 6L10 12" stroke="#64b5f6" strokeWidth="2"/>
      <path d="M4 18L10 12" stroke="#64b5f6" strokeWidth="2"/>
      <path d="M10 12L20 12" stroke="#64b5f6" strokeWidth="2"/>
      <circle cx="10" cy="12" r="2" fill="#64b5f6"/>
      <text x="2" y="3" fontSize="3" fill="#64b5f6">Supply</text>
    </svg>
  )
};

export default {
  connectorMixerConfig,
  zoneMixerConfig,
  supplySideMixerConfig
};
