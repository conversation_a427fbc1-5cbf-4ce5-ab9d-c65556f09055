service: hvac-designer-api

frameworkVersion: '3'

provider:
  name: aws
  runtime: python3.9
  stage: ${opt:stage, 'dev'}
  region: ${opt:region, 'us-east-1'}
  environment:
    SYSTEMS_TABLE: ${self:service}-${self:provider.stage}-systems
  iamRoleStatements:
    - Effect: Allow
      Action:
        - dynamodb:Query
        - dynamodb:Scan
        - dynamodb:GetItem
        - dynamodb:PutItem
        - dynamodb:UpdateItem
        - dynamodb:DeleteItem
      Resource: !GetAtt SystemsTable.Arn

functions:
  saveSystem:
    handler: handlers/system_handler.save_system
    events:
      - http:
          path: systems
          method: post
          cors: true

  getSystem:
    handler: handlers/system_handler.get_system
    events:
      - http:
          path: systems/{id}
          method: get
          cors: true

  listSystems:
    handler: handlers/system_handler.list_systems
    events:
      - http:
          path: systems
          method: get
          cors: true

  deleteSystem:
    handler: handlers/system_handler.delete_system
    events:
      - http:
          path: systems/{id}
          method: delete
          cors: true

  calculateEnergy:
    handler: handlers/energy_handler.calculate_energy
    events:
      - http:
          path: calculate/energy
          method: post
          cors: true
    timeout: 30  # Longer timeout for calculations

  parseIdd:
    handler: handlers/idd_handler.parse_idd
    events:
      - http:
          path: idd
          method: get
          cors: true

  listComponents:
    handler: handlers/idd_handler.list_components
    events:
      - http:
          path: idd/components
          method: get
          cors: true

resources:
  Resources:
    SystemsTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:provider.environment.SYSTEMS_TABLE}
        BillingMode: PAY_PER_REQUEST
        AttributeDefinitions:
          - AttributeName: id
            AttributeType: S
        KeySchema:
          - AttributeName: id
            KeyType: HASH

plugins:
  - serverless-python-requirements
  - serverless-offline

custom:
  pythonRequirements:
    dockerizePip: true
    slim: true
    noDeploy:
      - pytest
      - pytest-cov
      - pytest-mock
      - boto3
      - botocore
      - docutils
      - jmespath
      - pip
      - setuptools
      - wheel
  serverless-offline:
    httpPort: 4000
