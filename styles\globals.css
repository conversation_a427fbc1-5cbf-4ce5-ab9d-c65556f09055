* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html,
body {
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen,
    Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
  height: 100%;
  width: 100%;
}

#__next {
  height: 100%;
  width: 100%;
}

.app-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

.app-title {
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
  padding: 10px 20px;
  margin: 0;
  font-size: 1.5rem;
  font-weight: 500;
}

a {
  color: inherit;
  text-decoration: none;
}

button {
  cursor: pointer;
  border: none;
  outline: none;
  background: none;
  font-family: inherit;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
  font-size: 14px;
  transition: background-color 0.2s, color 0.2s;
}

.btn-primary {
  background-color: #1976d2;
  color: white;
}

.btn-primary:hover {
  background-color: #1565c0;
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #e0e0e0;
}

.btn-secondary:hover {
  background-color: #e0e0e0;
}

/* Tabbed properties styles */
.tabbed-properties {
  width: 100%;
}

.properties-tabs {
  margin-bottom: 16px;
}

.tab-content-container {
  padding: 16px;
  background-color: #f9f9f9;
  border: 1px solid #e0e0e0;
  border-radius: 0 0 4px 4px;
  border-top: none;
}

/* Make tabs look better */
.nav-tabs {
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
  border-bottom: 1px solid #dee2e6;
  margin-bottom: 0;
  padding-bottom: 0;
}

.nav-tabs .nav-item {
  flex: 0 0 auto;
  white-space: nowrap;
}

.nav-tabs .nav-link {
  margin-bottom: -1px;
  border: 1px solid transparent;
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-size: 0.9rem;
}

.nav-tabs .nav-link:hover {
  border-color: #e9ecef #e9ecef #dee2e6;
}

.nav-tabs .nav-link.active {
  color: #495057;
  background-color: #f9f9f9;
  border-color: #dee2e6 #dee2e6 #f9f9f9;
}

/* Selected reference styles */
.selected-reference {
  margin-bottom: 16px;
  padding: 8px;
  background-color: #e9f5ff;
  border-radius: 4px;
  font-size: 0.9rem;
}

.no-selection-message {
  padding: 16px;
  color: #666;
  font-style: italic;
  text-align: center;
}

.object-list-field {
  display: flex;
  align-items: center;
  gap: 8px;
}

.object-list-field select {
  flex: 1;
}

.note {
  margin-left: 4px;
  cursor: help;
  font-size: 14px;
  color: #666;
}
