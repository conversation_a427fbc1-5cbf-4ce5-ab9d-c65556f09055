import { generateNodeComponent } from '../../utils/componentGenerator';
import {
  airHandlerConfig,
  chillerConfig,
  boilerConfig,
  coolingTowerConfig,
  pumpConfig,
  vavConfig,
  zoneConfig
} from './nodeConfigs';
import { airLoopHVACConfig } from './AirLoopHVACConfig';
import { plantLoopConfig } from './PlantLoopConfig';
import { connectorMixerConfig, zoneMixerConfig, supplySideMixerConfig } from './MixerConfig';
import { connectorSplitterConfig, zoneSplitterConfig, supplySideSplitterConfig } from './SplitterConfig';
import { outdoorAirSystemConfig } from './OutdoorAirSystemConfig';
import { fanConstantVolumeConfig, fanVariableVolumeConfig } from './FanConfig';
import { coilCoolingWaterConfig, coilHeatingWaterConfig } from './CoilConfig';

// Generate node components from configurations
const AirHandlerNode = generateNodeComponent(
  airHandlerConfig.componentType,
  airHandlerConfig.nodeType,
  airHandlerConfig
);

const ChillerNode = generateNodeComponent(
  chillerConfig.componentType,
  chillerConfig.nodeType,
  chillerConfig
);

const BoilerNode = generateNodeComponent(
  boilerConfig.componentType,
  boilerConfig.nodeType,
  boilerConfig
);

const CoolingTowerNode = generateNodeComponent(
  coolingTowerConfig.componentType,
  coolingTowerConfig.nodeType,
  coolingTowerConfig
);

const PumpNode = generateNodeComponent(
  pumpConfig.componentType,
  pumpConfig.nodeType,
  pumpConfig
);

const VAVNode = generateNodeComponent(
  vavConfig.componentType,
  vavConfig.nodeType,
  vavConfig
);

const ZoneNode = generateNodeComponent(
  zoneConfig.componentType,
  zoneConfig.nodeType,
  zoneConfig
);

// Import custom nodes
import CustomAirLoopHVACNode from './AirLoopHVACNode';
import CustomPlantLoopNode from './PlantLoopNode';

// Mixer nodes
const ConnectorMixerNode = generateNodeComponent(
  connectorMixerConfig.componentType,
  connectorMixerConfig.nodeType,
  connectorMixerConfig
);

const ZoneMixerNode = generateNodeComponent(
  zoneMixerConfig.componentType,
  zoneMixerConfig.nodeType,
  zoneMixerConfig
);

const SupplySideMixerNode = generateNodeComponent(
  supplySideMixerConfig.componentType,
  supplySideMixerConfig.nodeType,
  supplySideMixerConfig
);

// Splitter nodes
const ConnectorSplitterNode = generateNodeComponent(
  connectorSplitterConfig.componentType,
  connectorSplitterConfig.nodeType,
  connectorSplitterConfig
);

const ZoneSplitterNode = generateNodeComponent(
  zoneSplitterConfig.componentType,
  zoneSplitterConfig.nodeType,
  zoneSplitterConfig
);

const SupplySideSplitterNode = generateNodeComponent(
  supplySideSplitterConfig.componentType,
  supplySideSplitterConfig.nodeType,
  supplySideSplitterConfig
);

const OutdoorAirSystemNode = generateNodeComponent(
  outdoorAirSystemConfig.componentType,
  outdoorAirSystemConfig.nodeType,
  outdoorAirSystemConfig
);

// Fan nodes
const FanConstantVolumeNode = generateNodeComponent(
  fanConstantVolumeConfig.componentType,
  fanConstantVolumeConfig.nodeType,
  fanConstantVolumeConfig
);

const FanVariableVolumeNode = generateNodeComponent(
  fanVariableVolumeConfig.componentType,
  fanVariableVolumeConfig.nodeType,
  fanVariableVolumeConfig
);

// Coil nodes
const CoilCoolingWaterNode = generateNodeComponent(
  coilCoolingWaterConfig.componentType,
  coilCoolingWaterConfig.nodeType,
  coilCoolingWaterConfig
);

const CoilHeatingWaterNode = generateNodeComponent(
  coilHeatingWaterConfig.componentType,
  coilHeatingWaterConfig.nodeType,
  coilHeatingWaterConfig
);

export const nodeTypes = {
  airHandler: AirHandlerNode,
  chiller: ChillerNode,
  boiler: BoilerNode,
  coolingTower: CoolingTowerNode,
  pump: PumpNode,
  vav: VAVNode,
  zone: ZoneNode,
  airLoopHVAC: CustomAirLoopHVACNode,
  plantLoop: CustomPlantLoopNode,
  connectorMixer: ConnectorMixerNode,
  zoneMixer: ZoneMixerNode,
  supplySideMixer: SupplySideMixerNode,
  connectorSplitter: ConnectorSplitterNode,
  zoneSplitter: ZoneSplitterNode,
  supplySideSplitter: SupplySideSplitterNode,
  outdoorAirSystem: OutdoorAirSystemNode,
  fanConstantVolume: FanConstantVolumeNode,
  fanVariableVolume: FanVariableVolumeNode,
  coilCoolingWater: CoilCoolingWaterNode,
  coilHeatingWater: CoilHeatingWaterNode
};
