import React, { memo } from 'react';
import { Hand<PERSON>, Position } from 'reactflow';

const VAVNode = ({ data, selected }) => {
  const { label, properties } = data;
  
  return (
    <div className={`vav-node ${selected ? 'selected' : ''}`}>
      {/* Input handles */}
      <Handle
        type="target"
        position={Position.Left}
        id="supply-air-in"
        className="handle air-handle"
        style={{ top: '50%' }}
      />
      
      <div className="node-content">
        <div className="node-header">{label}</div>
        <div className="node-body">
          <div className="node-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect x="3" y="6" width="18" height="12" rx="2" stroke="#7986cb" strokeWidth="2"/>
              <path d="M7 12H17" stroke="#7986cb" strokeWidth="2"/>
              <path d="M12 9L12 15" stroke="#7986cb" strokeWidth="2"/>
            </svg>
          </div>
          <div className="node-details">
            <div className="property-row">
              <span className="property-label">Airflow:</span>
              <span className="property-value">{properties.airflow} CFM</span>
            </div>
            <div className="property-row">
              <span className="property-label">Min Airflow:</span>
              <span className="property-value">{properties.minAirflow} CFM</span>
            </div>
          </div>
        </div>
      </div>
      
      {/* Output handles */}
      <Handle
        type="source"
        position={Position.Right}
        id="supply-air-out"
        className="handle air-handle"
        style={{ top: '50%' }}
      />
    </div>
  );
};

export default memo(VAVNode);

