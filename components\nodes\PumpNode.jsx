import React, { memo } from 'react';
import { Hand<PERSON>, Position } from 'reactflow';

const PumpNode = ({ data, selected }) => {
  const { label, properties } = data;
  
  return (
    <div className={`pump-node ${selected ? 'selected' : ''}`}>
      {/* Input handles */}
      <Handle
        type="target"
        position={Position.Left}
        id="water-in"
        className="handle"
        style={{ top: '50%' }}
      />
      
      <div className="node-content">
        <div className="node-header">{label}</div>
        <div className="node-body">
          <div className="node-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="6" stroke="#9575cd" strokeWidth="2"/>
              <path d="M12 6L12 18" stroke="#9575cd" strokeWidth="2"/>
              <path d="M9 9L15 15" stroke="#9575cd" strokeWidth="2"/>
              <path d="M15 9L9 15" stroke="#9575cd" strokeWidth="2"/>
            </svg>
          </div>
          <div className="node-details">
            <div className="property-row">
              <span className="property-label">Flow Rate:</span>
              <span className="property-value">{properties.flowRate} GPM</span>
            </div>
            <div className="property-row">
              <span className="property-label">Power:</span>
              <span className="property-value">{properties.power} HP</span>
            </div>
          </div>
        </div>
      </div>
      
      {/* Output handles */}
      <Handle
        type="source"
        position={Position.Right}
        id="water-out"
        className="handle"
        style={{ top: '50%' }}
      />
    </div>
  );
};

export default memo(PumpNode);

