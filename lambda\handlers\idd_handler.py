import json
import logging
import os
import re
from typing import Dict, List, Any, Optional, Tuple

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# IDD file path - this will be relative to the lambda function
IDD_FILE_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'OpenStudio.idd')

# Cache for parsed IDD data
idd_cache = None

def parse_idd(event, context):
    """AWS Lambda handler for parsing IDD file and returning component data"""
    try:
        # Parse query parameters
        query_params = event.get('queryStringParameters', {}) or {}
        component_type = query_params.get('type')
        
        # Get IDD data
        idd_data = get_idd_data()
        
        # Filter by component type if specified
        if component_type:
            if component_type in idd_data['components']:
                result = {
                    'components': {
                        component_type: idd_data['components'][component_type]
                    }
                }
            else:
                result = {
                    'components': {}
                }
        else:
            result = idd_data
        
        return {
            'statusCode': 200,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps(result)
        }
    except Exception as e:
        logger.error(f"Error parsing IDD file: {str(e)}")
        return {
            'statusCode': 500,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps({
                'success': False,
                'error': str(e)
            })
        }

def get_idd_data() -> Dict:
    """
    Get parsed IDD data, using cache if available
    
    Returns:
        Dictionary containing parsed IDD data
    """
    global idd_cache
    
    if idd_cache is not None:
        return idd_cache
    
    try:
        # Check if IDD file exists
        if not os.path.exists(IDD_FILE_PATH):
            # If file doesn't exist, return mock data
            logger.warning(f"IDD file not found at {IDD_FILE_PATH}, using mock data")
            idd_cache = get_mock_idd_data()
            return idd_cache
        
        # Parse IDD file
        idd_cache = parse_idd_file(IDD_FILE_PATH)
        return idd_cache
    except Exception as e:
        logger.error(f"Error reading IDD file: {str(e)}")
        # Return mock data if there's an error
        idd_cache = get_mock_idd_data()
        return idd_cache

def parse_idd_file(file_path: str) -> Dict:
    """
    Parse an IDD file and extract component definitions
    
    Args:
        file_path: Path to the IDD file
        
    Returns:
        Dictionary containing parsed IDD data
    """
    components = {}
    current_component = None
    current_field = None
    
    # Regular expressions for parsing
    component_re = re.compile(r'^\s*\\group\s+(.+)$|^\s*([A-Za-z0-9:_]+),\s*$')
    field_re = re.compile(r'^\s*([A-Za-z0-9:_]+)\s*;\s*$')
    field_attr_re = re.compile(r'^\s*\\([\w-]+)(?:\s+(.+))?$')
    
    with open(file_path, 'r') as f:
        for line in f:
            line = line.strip()
            
            # Skip empty lines and comments
            if not line or line.startswith('!'):
                continue
            
            # Check if this is a new component
            component_match = component_re.match(line)
            if component_match:
                group_name = component_match.group(1)
                component_name = component_match.group(2)
                
                if component_name:
                    # Start a new component
                    current_component = {
                        'name': component_name,
                        'group': current_component['group'] if current_component else '',
                        'fields': {}
                    }
                    components[component_name] = current_component
                    current_field = None
                elif group_name:
                    # Update group name for subsequent components
                    if current_component:
                        current_component['group'] = group_name
                continue
            
            # Check if this is a new field
            field_match = field_re.match(line)
            if field_match and current_component:
                field_name = field_match.group(1)
                current_field = {
                    'name': field_name,
                    'type': 'string',  # Default type
                    'required': False,
                    'default': None,
                    'units': None,
                    'note': None,
                    'options': []
                }
                current_component['fields'][field_name] = current_field
                continue
            
            # Check if this is a field attribute
            attr_match = field_attr_re.match(line)
            if attr_match and current_field:
                attr_name = attr_match.group(1)
                attr_value = attr_match.group(2)
                
                if attr_name == 'required-field':
                    current_field['required'] = True
                elif attr_name == 'type':
                    current_field['type'] = attr_value
                elif attr_name == 'units':
                    current_field['units'] = attr_value
                elif attr_name == 'default':
                    current_field['default'] = attr_value
                elif attr_name == 'note':
                    current_field['note'] = attr_value
                elif attr_name == 'key':
                    current_field['options'].append(attr_value)
    
    # Filter to only include HVAC components
    hvac_components = {}
    hvac_groups = ['HVAC', 'HVAC Templates', 'HVAC Design Objects', 'Node-Branch Management']
    
    for name, component in components.items():
        if component['group'] in hvac_groups or name.startswith('OS:'):
            hvac_components[name] = component
    
    return {
        'components': hvac_components
    }

def get_mock_idd_data() -> Dict:
    """
    Get mock IDD data for testing
    
    Returns:
        Dictionary containing mock IDD data
    """
    return {
        'components': {
            'OS:AirLoopHVAC': {
                'name': 'OS:AirLoopHVAC',
                'group': 'HVAC',
                'fields': {
                    'Name': {
                        'name': 'Name',
                        'type': 'string',
                        'required': True,
                        'default': None,
                        'units': None,
                        'note': None,
                        'options': []
                    },
                    'DesignSupplyAirFlowRate': {
                        'name': 'DesignSupplyAirFlowRate',
                        'type': 'real',
                        'required': False,
                        'default': 'autosize',
                        'units': 'm3/s',
                        'note': None,
                        'options': []
                    },
                    'AvailabilitySchedule': {
                        'name': 'AvailabilitySchedule',
                        'type': 'object-list',
                        'required': False,
                        'default': None,
                        'units': None,
                        'note': None,
                        'options': ['OS:Schedule:Constant', 'OS:Schedule:Compact']
                    },
                    'NightCycleControlType': {
                        'name': 'NightCycleControlType',
                        'type': 'choice',
                        'required': False,
                        'default': 'StayOff',
                        'units': None,
                        'note': None,
                        'options': ['StayOff', 'CycleOnAny', 'CycleOnControlZone']
                    }
                }
            },
            'OS:AirLoopHVAC:UnitarySystem': {
                'name': 'OS:AirLoopHVAC:UnitarySystem',
                'group': 'HVAC',
                'fields': {
                    'Name': {
                        'name': 'Name',
                        'type': 'string',
                        'required': True,
                        'default': None,
                        'units': None,
                        'note': None,
                        'options': []
                    },
                    'ControlType': {
                        'name': 'ControlType',
                        'type': 'choice',
                        'required': True,
                        'default': 'Load',
                        'units': None,
                        'note': None,
                        'options': ['Load', 'SetPoint', 'SingleZoneVAV']
                    },
                    'DehumidificationControlType': {
                        'name': 'DehumidificationControlType',
                        'type': 'choice',
                        'required': False,
                        'default': 'None',
                        'units': None,
                        'note': None,
                        'options': ['None', 'CoolReheat']
                    },
                    'FanPlacement': {
                        'name': 'FanPlacement',
                        'type': 'choice',
                        'required': False,
                        'default': 'BlowThrough',
                        'units': None,
                        'note': None,
                        'options': ['BlowThrough', 'DrawThrough']
                    },
                    'CoolingCoil': {
                        'name': 'CoolingCoil',
                        'type': 'object-list',
                        'required': False,
                        'default': None,
                        'units': None,
                        'note': None,
                        'options': ['OS:Coil:Cooling:Water', 'OS:Coil:Cooling:DX']
                    },
                    'HeatingCoil': {
                        'name': 'HeatingCoil',
                        'type': 'object-list',
                        'required': False,
                        'default': None,
                        'units': None,
                        'note': None,
                        'options': ['OS:Coil:Heating:Water', 'OS:Coil:Heating:Gas']
                    },
                    'SupplyFan': {
                        'name': 'SupplyFan',
                        'type': 'object-list',
                        'required': False,
                        'default': None,
                        'units': None,
                        'note': None,
                        'options': ['OS:Fan:ConstantVolume', 'OS:Fan:VariableVolume']
                    }
                }
            },
            'OS:Chiller:Electric:EIR': {
                'name': 'OS:Chiller:Electric:EIR',
                'group': 'HVAC',
                'fields': {
                    'Name': {
                        'name': 'Name',
                        'type': 'string',
                        'required': True,
                        'default': None,
                        'units': None,
                        'note': None,
                        'options': []
                    },
                    'ReferenceCapacity': {
                        'name': 'ReferenceCapacity',
                        'type': 'real',
                        'required': False,
                        'default': 'autosize',
                        'units': 'W',
                        'note': None,
                        'options': []
                    },
                    'ReferenceCOP': {
                        'name': 'ReferenceCOP',
                        'type': 'real',
                        'required': True,
                        'default': None,
                        'units': 'W/W',
                        'note': None,
                        'options': []
                    },
                    'MinimumPartLoadRatio': {
                        'name': 'MinimumPartLoadRatio',
                        'type': 'real',
                        'required': False,
                        'default': 0.1,
                        'units': None,
                        'note': None,
                        'options': []
                    },
                    'MaximumPartLoadRatio': {
                        'name': 'MaximumPartLoadRatio',
                        'type': 'real',
                        'required': False,
                        'default': 1.0,
                        'units': None,
                        'note': None,
                        'options': []
                    },
                    'OptimumPartLoadRatio': {
                        'name': 'OptimumPartLoadRatio',
                        'type': 'real',
                        'required': False,
                        'default': 1.0,
                        'units': None,
                        'note': None,
                        'options': []
                    },
                    'CondenserType': {
                        'name': 'CondenserType',
                        'type': 'choice',
                        'required': False,
                        'default': 'WaterCooled',
                        'units': None,
                        'note': None,
                        'options': ['AirCooled', 'WaterCooled', 'EvaporativelyCooled']
                    }
                }
            },
            'OS:Boiler:HotWater': {
                'name': 'OS:Boiler:HotWater',
                'group': 'HVAC',
                'fields': {
                    'Name': {
                        'name': 'Name',
                        'type': 'string',
                        'required': True,
                        'default': None,
                        'units': None,
                        'note': None,
                        'options': []
                    },
                    'NominalCapacity': {
                        'name': 'NominalCapacity',
                        'type': 'real',
                        'required': False,
                        'default': 'autosize',
                        'units': 'W',
                        'note': None,
                        'options': []
                    },
                    'FuelType': {
                        'name': 'FuelType',
                        'type': 'choice',
                        'required': True,
                        'default': 'NaturalGas',
                        'units': None,
                        'note': None,
                        'options': ['NaturalGas', 'Electricity', 'PropaneGas', 'FuelOil#1', 'FuelOil#2', 'Coal', 'Diesel', 'Gasoline', 'OtherFuel1', 'OtherFuel2']
                    },
                    'NominalThermalEfficiency': {
                        'name': 'NominalThermalEfficiency',
                        'type': 'real',
                        'required': False,
                        'default': 0.8,
                        'units': None,
                        'note': None,
                        'options': []
                    },
                    'DesignWaterOutletTemperature': {
                        'name': 'DesignWaterOutletTemperature',
                        'type': 'real',
                        'required': False,
                        'default': 82.0,
                        'units': 'C',
                        'note': None,
                        'options': []
                    },
                    'MaximumPartLoadRatio': {
                        'name': 'MaximumPartLoadRatio',
                        'type': 'real',
                        'required': False,
                        'default': 1.0,
                        'units': None,
                        'note': None,
                        'options': []
                    }
                }
            },
            'OS:Fan:ConstantVolume': {
                'name': 'OS:Fan:ConstantVolume',
                'group': 'HVAC',
                'fields': {
                    'Name': {
                        'name': 'Name',
                        'type': 'string',
                        'required': True,
                        'default': None,
                        'units': None,
                        'note': None,
                        'options': []
                    },
                    'FanEfficiency': {
                        'name': 'FanEfficiency',
                        'type': 'real',
                        'required': True,
                        'default': 0.7,
                        'units': None,
                        'note': None,
                        'options': []
                    },
                    'PressureRise': {
                        'name': 'PressureRise',
                        'type': 'real',
                        'required': True,
                        'default': 500,
                        'units': 'Pa',
                        'note': None,
                        'options': []
                    },
                    'MotorEfficiency': {
                        'name': 'MotorEfficiency',
                        'type': 'real',
                        'required': True,
                        'default': 0.9,
                        'units': None,
                        'note': None,
                        'options': []
                    },
                    'MaximumFlowRate': {
                        'name': 'MaximumFlowRate',
                        'type': 'real',
                        'required': False,
                        'default': 'autosize',
                        'units': 'm3/s',
                        'note': None,
                        'options': []
                    }
                }
            },
            'OS:Fan:VariableVolume': {
                'name': 'OS:Fan:VariableVolume',
                'group': 'HVAC',
                'fields': {
                    'Name': {
                        'name': 'Name',
                        'type': 'string',
                        'required': True,
                        'default': None,
                        'units': None,
                        'note': None,
                        'options': []
                    },
                    'FanEfficiency': {
                        'name': 'FanEfficiency',
                        'type': 'real',
                        'required': True,
                        'default': 0.7,
                        'units': None,
                        'note': None,
                        'options': []
                    },
                    'PressureRise': {
                        'name': 'PressureRise',
                        'type': 'real',
                        'required': True,
                        'default': 500,
                        'units': 'Pa',
                        'note': None,
                        'options': []
                    },
                    'MotorEfficiency': {
                        'name': 'MotorEfficiency',
                        'type': 'real',
                        'required': True,
                        'default': 0.9,
                        'units': None,
                        'note': None,
                        'options': []
                    },
                    'MaximumFlowRate': {
                        'name': 'MaximumFlowRate',
                        'type': 'real',
                        'required': False,
                        'default': 'autosize',
                        'units': 'm3/s',
                        'note': None,
                        'options': []
                    },
                    'FanPowerMinimumFlowRateInputMethod': {
                        'name': 'FanPowerMinimumFlowRateInputMethod',
                        'type': 'choice',
                        'required': False,
                        'default': 'Fraction',
                        'units': None,
                        'note': None,
                        'options': ['Fraction', 'FixedFlowRate']
                    },
                    'FanPowerMinimumFlowFraction': {
                        'name': 'FanPowerMinimumFlowFraction',
                        'type': 'real',
                        'required': False,
                        'default': 0.25,
                        'units': None,
                        'note': None,
                        'options': []
                    }
                }
            },
            'OS:PlantLoop': {
                'name': 'OS:PlantLoop',
                'group': 'HVAC',
                'fields': {
                    'Name': {
                        'name': 'Name',
                        'type': 'string',
                        'required': True,
                        'default': None,
                        'units': None,
                        'note': None,
                        'options': []
                    },
                    'FluidType': {
                        'name': 'FluidType',
                        'type': 'choice',
                        'required': False,
                        'default': 'Water',
                        'units': None,
                        'note': None,
                        'options': ['Water', 'Steam', 'UserDefinedFluidType']
                    },
                    'LoopTemperatureSetpointNodeName': {
                        'name': 'LoopTemperatureSetpointNodeName',
                        'type': 'node',
                        'required': False,
                        'default': None,
                        'units': None,
                        'note': None,
                        'options': []
                    },
                    'MaximumLoopTemperature': {
                        'name': 'MaximumLoopTemperature',
                        'type': 'real',
                        'required': False,
                        'default': 82.0,
                        'units': 'C',
                        'note': None,
                        'options': []
                    },
                    'MinimumLoopTemperature': {
                        'name': 'MinimumLoopTemperature',
                        'type': 'real',
                        'required': False,
                        'default': 5.0,
                        'units': 'C',
                        'note': None,
                        'options': []
                    },
                    'MaximumLoopFlowRate': {
                        'name': 'MaximumLoopFlowRate',
                        'type': 'real',
                        'required': False,
                        'default': 'autosize',
                        'units': 'm3/s',
                        'note': None,
                        'options': []
                    },
                    'MinimumLoopFlowRate': {
                        'name': 'MinimumLoopFlowRate',
                        'type': 'real',
                        'required': False,
                        'default': 0.0,
                        'units': 'm3/s',
                        'note': None,
                        'options': []
                    },
                    'LoadDistributionScheme': {
                        'name': 'LoadDistributionScheme',
                        'type': 'choice',
                        'required': False,
                        'default': 'SequentialLoad',
                        'units': None,
                        'note': None,
                        'options': ['Optimal', 'SequentialLoad', 'UniformLoad', 'UniformPLR', 'SequentialUniformPLR']
                    }
                }
            }
        }
    }

def list_components(event, context):
    """AWS Lambda handler for listing available component types"""
    try:
        # Get IDD data
        idd_data = get_idd_data()
        
        # Extract component names and groups
        components = []
        for name, component in idd_data['components'].items():
            components.append({
                'name': name,
                'group': component.get('group', '')
            })
        
        # Sort by group then name
        components.sort(key=lambda x: (x['group'], x['name']))
        
        return {
            'statusCode': 200,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps({
                'success': True,
                'components': components
            })
        }
    except Exception as e:
        logger.error(f"Error listing components: {str(e)}")
        return {
            'statusCode': 500,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps({
                'success': False,
                'error': str(e)
            })
        }
