import React from 'react';
import { Position } from 'reactflow';

// OutdoorAirSystem Node Configuration
export const outdoorAirSystemConfig = {
  componentType: 'OS:AirLoopHVAC:OutdoorAirSystem',
  nodeType: 'outdoor-air-system',
  inputs: [
    {
      id: 'return-air-in',
      position: Position.Left,
      className: 'air-handle',
      style: { top: '70%' }
    },
    {
      id: 'outdoor-air-in',
      position: Position.Top,
      className: 'air-handle',
      style: { left: '50%' }
    }
  ],
  outputs: [
    {
      id: 'mixed-air-out',
      position: Position.Right,
      className: 'air-handle',
      style: { top: '50%' }
    },
    {
      id: 'relief-air-out',
      position: Position.Bottom,
      className: 'air-handle',
      style: { left: '50%' }
    }
  ],
  displayProperties: [
    {
      name: 'controllerName',
      label: 'Controller',
      unit: ''
    },
    {
      name: 'minimumOutdoorAirFlowRate',
      label: 'Min OA',
      unit: 'CFM',
      format: (value) => value
    }
  ],
  icon: (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x="4" y="4" width="16" height="16" rx="2" stroke="#64b5f6" strokeWidth="2"/>
      <path d="M4 10H20" stroke="#64b5f6" strokeWidth="2"/>
      <path d="M4 14H20" stroke="#64b5f6" strokeWidth="2"/>
      <path d="M12 4V1" stroke="#64b5f6" strokeWidth="2"/>
      <path d="M12 23V20" stroke="#64b5f6" strokeWidth="2"/>
      <path d="M8 1L16 1" stroke="#64b5f6" strokeWidth="2"/>
    </svg>
  )
};

export default outdoorAirSystemConfig;
