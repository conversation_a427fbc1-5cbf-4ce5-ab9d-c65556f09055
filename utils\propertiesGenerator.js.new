import React, { useEffect, useState } from 'react';
import { getComponentMetadataSync } from './iddParser';
import iddService from '../services/iddService';

/**
 * Generate property fields for a component type
 * @param {string} componentType - The component type
 * @param {Object} properties - The current property values
 * @param {Function} handlePropertyChange - Function to handle property changes
 * @returns {JSX.Element} - The property fields
 */
export const generatePropertyFields = (componentType, properties, handlePropertyChange) => {
  // If no component type is provided, return null
  if (!componentType) {
    return null;
  }
  
  // Ensure properties is an object
  const props = properties || {};
  
  const [iddFields, setIddFields] = useState(null);
  const [loading, setLoading] = useState(true);
  
  // Get component metadata synchronously for initial render
  const componentDef = getComponentMetadataSync(componentType);
  
  // Fetch IDD fields asynchronously
  useEffect(() => {
    const fetchIddFields = async () => {
      try {
        // Convert our internal component type to OpenStudio component type
        const osComponentType = iddService.componentTypeMap[componentType] || componentType;
        
        // Fetch component properties from the IDD file
        const component = await iddService.getComponentProperties(osComponentType);
        
        if (component && component.fields) {
          setIddFields(component.fields);
        }
        
        setLoading(false);
      } catch (error) {
        console.error(`Error fetching IDD fields for ${componentType}:`, error);
        setLoading(false);
      }
    };
    
    fetchIddFields();
  }, [componentType]);
  
  // If we have IDD fields, use them to generate property fields
  if (iddFields) {
    return (
      <>
        {Object.entries(iddFields).map(([fieldName, field]) => {
          // Skip the Name field as it's handled separately
          if (fieldName === 'Name') return null;
          
          // Convert field name to camelCase for property name
          const propName = fieldName.charAt(0).toLowerCase() + fieldName.slice(1);
          
          // Get the current property value or use the default
          const value = props[propName] !== undefined ? props[propName] : field.default;
          
          // Generate the appropriate input field based on the field type
          return (
            <div className="property-field" key={fieldName}>
              <label>
                {fieldName}
                {field.required && <span className="required">*</span>}
                {field.units && <span className="units"> ({field.units})</span>}
              </label>
              
              {renderFieldInput(field, propName, value, handlePropertyChange)}
            </div>
          );
        })}
      </>
    );
  }
  
  // If we're still loading or couldn't get IDD fields, fall back to hardcoded fields
  if (loading) {
    return <div className="loading-properties">Loading properties...</div>;
  }
  
  // Fall back to our existing property fields
  switch (componentType) {
    case 'airHandler':
      return (
        <>
          <div className="property-field">
            <label>Airflow (CFM)</label>
            <input
              type="number"
              value={props.airflow}
              onChange={(e) => handlePropertyChange('airflow', e.target.value)}
            />
          </div>
          <div className="property-field">
            <label>Fan Power (HP)</label>
            <input
              type="number"
              step="0.1"
              value={props.fanPower}
              onChange={(e) => handlePropertyChange('fanPower', e.target.value)}
            />
          </div>
          <div className="property-field">
            <label>Cooling Capacity (BTU/h)</label>
            <input
              type="number"
              step="1000"
              value={props.coolingCapacity}
              onChange={(e) => handlePropertyChange('coolingCapacity', e.target.value)}
            />
          </div>
          <div className="property-field">
            <label>Heating Capacity (BTU/h)</label>
            <input
              type="number"
              step="1000"
              value={props.heatingCapacity}
              onChange={(e) => handlePropertyChange('heatingCapacity', e.target.value)}
            />
          </div>
          <div className="property-field">
            <label>Efficiency</label>
            <input
              type="number"
              step="0.01"
              min="0"
              max="1"
              value={props.efficiency}
              onChange={(e) => handlePropertyChange('efficiency', e.target.value)}
            />
          </div>
        </>
      );

    case 'chiller':
      return (
        <>
          <div className="property-field">
            <label>Capacity (Tons)</label>
            <input
              type="number"
              value={props.capacity}
              onChange={(e) => handlePropertyChange('capacity', e.target.value)}
            />
          </div>
          <div className="property-field">
            <label>COP</label>
            <input
              type="number"
              step="0.1"
              value={props.cop}
              onChange={(e) => handlePropertyChange('cop', e.target.value)}
            />
          </div>
          <div className="property-field">
            <label>Type</label>
            <select
              value={props.type}
              onChange={(e) => handlePropertyChange('type', e.target.value)}
            >
              <option value="water-cooled">Water Cooled</option>
              <option value="air-cooled">Air Cooled</option>
            </select>
          </div>
          <div className="property-field">
            <label>Min Part Load Ratio</label>
            <input
              type="number"
              step="0.01"
              min="0"
              max="1"
              value={props.minPartLoadRatio}
              onChange={(e) => handlePropertyChange('minPartLoadRatio', e.target.value)}
            />
          </div>
          <div className="property-field">
            <label>Optimal Part Load Ratio</label>
            <input
              type="number"
              step="0.01"
              min="0"
              max="1"
              value={props.optimalPartLoadRatio}
              onChange={(e) => handlePropertyChange('optimalPartLoadRatio', e.target.value)}
            />
          </div>
        </>
      );

    case 'boiler':
      return (
        <>
          <div className="property-field">
            <label>Capacity (BTU/h)</label>
            <input
              type="number"
              step="1000"
              value={props.capacity}
              onChange={(e) => handlePropertyChange('capacity', e.target.value)}
            />
          </div>
          <div className="property-field">
            <label>Efficiency</label>
            <input
              type="number"
              step="0.01"
              min="0"
              max="1"
              value={props.efficiency}
              onChange={(e) => handlePropertyChange('efficiency', e.target.value)}
            />
          </div>
          <div className="property-field">
            <label>Fuel Type</label>
            <select
              value={props.fuelType}
              onChange={(e) => handlePropertyChange('fuelType', e.target.value)}
            >
              <option value="natural-gas">Natural Gas</option>
              <option value="electricity">Electricity</option>
              <option value="fuel-oil">Fuel Oil</option>
            </select>
          </div>
          <div className="property-field">
            <label>Max Temperature (°F)</label>
            <input
              type="number"
              value={props.maxTemperature}
              onChange={(e) => handlePropertyChange('maxTemperature', e.target.value)}
            />
          </div>
        </>
      );

    case 'coolingTower':
      return (
        <>
          <div className="property-field">
            <label>Capacity (Tons)</label>
            <input
              type="number"
              value={props.capacity}
              onChange={(e) => handlePropertyChange('capacity', e.target.value)}
            />
          </div>
          <div className="property-field">
            <label>Fan Power (HP)</label>
            <input
              type="number"
              step="0.1"
              value={props.fanPower}
              onChange={(e) => handlePropertyChange('fanPower', e.target.value)}
            />
          </div>
          <div className="property-field">
            <label>Type</label>
            <select
              value={props.type}
              onChange={(e) => handlePropertyChange('type', e.target.value)}
            >
              <option value="open-circuit">Open Circuit</option>
              <option value="closed-circuit">Closed Circuit</option>
            </select>
          </div>
          <div className="property-field">
            <label>Approach (°F)</label>
            <input
              type="number"
              step="0.1"
              value={props.approach}
              onChange={(e) => handlePropertyChange('approach', e.target.value)}
            />
          </div>
        </>
      );

    case 'pump':
      return (
        <>
          <div className="property-field">
            <label>Flow Rate (GPM)</label>
            <input
              type="number"
              value={props.flowRate}
              onChange={(e) => handlePropertyChange('flowRate', e.target.value)}
            />
          </div>
          <div className="property-field">
            <label>Head (ft)</label>
            <input
              type="number"
              value={props.head}
              onChange={(e) => handlePropertyChange('head', e.target.value)}
            />
          </div>
          <div className="property-field">
            <label>Power (HP)</label>
            <input
              type="number"
              step="0.1"
              value={props.power}
              onChange={(e) => handlePropertyChange('power', e.target.value)}
            />
          </div>
          <div className="property-field">
            <label>Efficiency</label>
            <input
              type="number"
              step="0.01"
              min="0"
              max="1"
              value={props.efficiency}
              onChange={(e) => handlePropertyChange('efficiency', e.target.value)}
            />
          </div>
        </>
      );

    case 'vav':
      return (
        <>
          <div className="property-field">
            <label>Airflow (CFM)</label>
            <input
              type="number"
              value={props.airflow}
              onChange={(e) => handlePropertyChange('airflow', e.target.value)}
            />
          </div>
          <div className="property-field">
            <label>Min Airflow (CFM)</label>
            <input
              type="number"
              value={props.minAirflow}
              onChange={(e) => handlePropertyChange('minAirflow', e.target.value)}
            />
          </div>
          <div className="property-field">
            <label>Reheat Capacity (BTU/h)</label>
            <input
              type="number"
              step="1000"
              value={props.reheatCapacity}
              onChange={(e) => handlePropertyChange('reheatCapacity', e.target.value)}
            />
          </div>
          <div className="property-field">
            <label>Zone Temperature (°F)</label>
            <input
              type="number"
              step="0.1"
              value={props.zoneTemperature}
              onChange={(e) => handlePropertyChange('zoneTemperature', e.target.value)}
            />
          </div>
        </>
      );

    case 'zone':
      return (
        <>
          <div className="property-field">
            <label>Area (ft²)</label>
            <input
              type="number"
              value={props.area}
              onChange={(e) => handlePropertyChange('area', e.target.value)}
            />
          </div>
          <div className="property-field">
            <label>Peak Cooling Load (BTU/h)</label>
            <input
              type="number"
              step="1000"
              value={props.peakCoolingLoad}
              onChange={(e) => handlePropertyChange('peakCoolingLoad', e.target.value)}
            />
          </div>
          <div className="property-field">
            <label>Peak Heating Load (BTU/h)</label>
            <input
              type="number"
              step="1000"
              value={props.peakHeatingLoad}
              onChange={(e) => handlePropertyChange('peakHeatingLoad', e.target.value)}
            />
          </div>
          <div className="property-field">
            <label>Occupancy (people)</label>
            <input
              type="number"
              value={props.occupancy}
              onChange={(e) => handlePropertyChange('occupancy', e.target.value)}
            />
          </div>
        </>
      );

    default:
      return null;
  }
};

/**
 * Render an input field based on the field type
 * @param {Object} field - The field definition from the IDD file
 * @param {string} propName - The property name
 * @param {any} value - The current value
 * @param {Function} handlePropertyChange - Function to handle property changes
 * @returns {JSX.Element} - The input field
 */
const renderFieldInput = (field, propName, value, handlePropertyChange) => {
  // Handle different field types
  switch (field.type) {
    case 'choice':
      return (
        <select
          value={value || field.default || ''}
          onChange={(e) => handlePropertyChange(propName, e.target.value)}
        >
          {field.options.map(option => (
            <option key={option} value={option}>{option}</option>
          ))}
        </select>
      );
      
    case 'real':
      return (
        <input
          type="number"
          step="0.01"
          value={value !== undefined ? value : ''}
          onChange={(e) => handlePropertyChange(propName, e.target.value)}
        />
      );
      
    case 'integer':
      return (
        <input
          type="number"
          step="1"
          value={value !== undefined ? value : ''}
          onChange={(e) => handlePropertyChange(propName, e.target.value)}
        />
      );
      
    case 'alpha':
    case 'string':
      return (
        <input
          type="text"
          value={value || ''}
          onChange={(e) => handlePropertyChange(propName, e.target.value)}
        />
      );
      
    case 'object-list':
      // For object-list, we could potentially fetch a list of valid objects
      // For now, just use a text input
      return (
        <input
          type="text"
          value={value || ''}
          onChange={(e) => handlePropertyChange(propName, e.target.value)}
        />
      );
      
    default:
      return (
        <input
          type="text"
          value={value || ''}
          onChange={(e) => handlePropertyChange(propName, e.target.value)}
        />
      );
  }
};

export default {
  generatePropertyFields
};
