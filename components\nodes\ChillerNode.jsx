import React, { memo } from 'react';
import { Hand<PERSON>, Position } from 'reactflow';

const ChillerNode = ({ data, selected }) => {
  const { label, properties } = data;
  
  return (
    <div className={`chiller-node ${selected ? 'selected' : ''}`}>
      {/* Input handles */}
      <Handle
        type="target"
        position={Position.Top}
        id="condenser-water-in"
        className="handle condenser-water-handle"
        style={{ left: '30%' }}
      />
      <Handle
        type="target"
        position={Position.Left}
        id="chilled-water-in"
        className="handle chilled-water-handle"
        style={{ top: '50%' }}
      />
      
      <div className="node-content">
        <div className="node-header">{label}</div>
        <div className="node-body">
          <div className="node-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect x="3" y="3" width="18" height="18" rx="2" stroke="#4fc3f7" strokeWidth="2"/>
              <path d="M7 7L17 17" stroke="#4fc3f7" strokeWidth="2"/>
              <path d="M17 7L7 17" stroke="#4fc3f7" strokeWidth="2"/>
            </svg>
          </div>
          <div className="node-details">
            <div className="property-row">
              <span className="property-label">Capacity:</span>
              <span className="property-value">{properties.capacity} Tons</span>
            </div>
            <div className="property-row">
              <span className="property-label">COP:</span>
              <span className="property-value">{properties.cop}</span>
            </div>
          </div>
        </div>
      </div>
      
      {/* Output handles */}
      <Handle
        type="source"
        position={Position.Right}
        id="chilled-water-out"
        className="handle chilled-water-handle"
        style={{ top: '50%' }}
      />
      <Handle
        type="source"
        position={Position.Bottom}
        id="condenser-water-out"
        className="handle condenser-water-handle"
        style={{ left: '70%' }}
      />
    </div>
  );
};

export default memo(ChillerNode);

