import React from 'react';
import { Position } from 'reactflow';

// AirLoopHVAC Node Configuration
export const airLoopHVACConfig = {
  componentType: 'OS:AirLoopHVAC',
  nodeType: 'air-loop-hvac',
  // An AirLoopHVAC has both supply and demand sides
  supplyComponents: [], // Will be populated when components are added
  demandComponents: [], // Will be populated when components are added
  // This is a container component that can have other components added to it
  isContainer: true,
  // Set this to true to indicate this is a parent node
  isParent: true,
  // Default dimensions for the parent node
  style: {
    width: 800,
    height: 500,
    padding: 20
  },
  // Define the supply and demand sides
  sides: {
    supply: {
      title: 'Supply Side',
      position: { top: 0, left: 0, right: 0, height: '50%' },
      allowedComponents: [
        'OS:Fan:ConstantVolume',
        'OS:Fan:VariableVolume',
        'OS:Coil:Cooling:Water',
        'OS:Coil:Heating:Water',
        'OS:AirLoopHVAC:OutdoorAirSystem',
        'OS:Connector:Mixer',
        'OS:Connector:Splitter'
      ]
    },
    demand: {
      title: 'Demand Side',
      position: { bottom: 0, left: 0, right: 0, height: '50%' },
      allowedComponents: [
        'OS:AirTerminal:SingleDuct:VAV:Reheat',
        'OS:ThermalZone',
        'OS:AirLoopHVAC:ZoneMixer',
        'OS:AirLoopHVAC:ZoneSplitter'
      ]
    }
  },
  // List of all component types that can be added inside this node
  allowedComponents: [
    'OS:Fan:ConstantVolume',
    'OS:Fan:VariableVolume',
    'OS:Coil:Cooling:Water',
    'OS:Coil:Heating:Water',
    'OS:AirLoopHVAC:OutdoorAirSystem',
    'OS:Connector:Mixer',
    'OS:Connector:Splitter',
    'OS:AirTerminal:SingleDuct:VAV:Reheat',
    'OS:ThermalZone',
    'OS:AirLoopHVAC:ZoneMixer',
    'OS:AirLoopHVAC:ZoneSplitter'
  ],
  inputs: [
    {
      id: 'supply-inlet',
      position: Position.Left,
      className: 'air-handle',
      style: { top: '30%' },
      side: 'supply'
    },
    {
      id: 'demand-outlet',
      position: Position.Left,
      className: 'air-handle',
      style: { top: '70%' },
      side: 'demand'
    }
  ],
  outputs: [
    {
      id: 'supply-outlet',
      position: Position.Right,
      className: 'air-handle',
      style: { top: '30%' },
      side: 'supply'
    },
    {
      id: 'demand-inlet',
      position: Position.Right,
      className: 'air-handle',
      style: { top: '70%' },
      side: 'demand'
    }
  ],
  displayProperties: [
    {
      name: 'designSupplyAirFlowRate',
      label: 'Design Flow',
      unit: 'CFM',
      format: (value) => value
    },
    {
      name: 'availabilitySchedule',
      label: 'Schedule',
      unit: ''
    },
    {
      name: 'nightCycleControlType',
      label: 'Night Cycle',
      unit: ''
    }
  ],
  icon: (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x="2" y="4" width="20" height="16" rx="2" stroke="#4fc3f7" strokeWidth="2"/>
      <path d="M6 8L18 8" stroke="#4fc3f7" strokeWidth="2"/>
      <path d="M6 12L18 12" stroke="#4fc3f7" strokeWidth="2"/>
      <path d="M6 16L18 16" stroke="#4fc3f7" strokeWidth="2"/>
      <path d="M12 4L12 8" stroke="#4fc3f7" strokeWidth="2"/>
    </svg>
  )
};

export default airLoopHVACConfig;
