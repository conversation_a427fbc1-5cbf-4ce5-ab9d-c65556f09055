import React from 'react';
import { Position } from 'reactflow';

// Coil:Cooling:Water Node Configuration
export const coilCoolingWaterConfig = {
  componentType: 'OS:Coil:Cooling:Water',
  nodeType: 'coil-cooling-water',
  inputs: [
    {
      id: 'air-inlet',
      position: Position.Left,
      className: 'air-handle',
      style: { top: '30%' }
    },
    {
      id: 'water-inlet',
      position: Position.Bottom,
      className: 'chilled-water-handle',
      style: { left: '30%' }
    }
  ],
  outputs: [
    {
      id: 'air-outlet',
      position: Position.Right,
      className: 'air-handle',
      style: { top: '30%' }
    },
    {
      id: 'water-outlet',
      position: Position.Bottom,
      className: 'chilled-water-handle',
      style: { left: '70%' }
    }
  ],
  displayProperties: [
    {
      name: 'designWaterFlowRate',
      label: 'Water Flow',
      unit: 'L/s',
      format: (value) => value
    },
    {
      name: 'designAirFlowRate',
      label: 'Air Flow',
      unit: 'm³/s',
      format: (value) => value
    },
    {
      name: 'designInletWaterTemperature',
      label: 'Water Temp',
      unit: '°C',
      format: (value) => value
    }
  ],
  icon: (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x="4" y="6" width="16" height="12" rx="2" stroke="#4fc3f7" strokeWidth="2"/>
      <path d="M4 10L20 10" stroke="#4fc3f7" strokeWidth="2"/>
      <path d="M4 14L20 14" stroke="#4fc3f7" strokeWidth="2"/>
      <path d="M8 6L8 18" stroke="#4fc3f7" strokeWidth="2"/>
      <path d="M12 6L12 18" stroke="#4fc3f7" strokeWidth="2"/>
      <path d="M16 6L16 18" stroke="#4fc3f7" strokeWidth="2"/>
    </svg>
  )
};

// Coil:Heating:Water Node Configuration
export const coilHeatingWaterConfig = {
  componentType: 'OS:Coil:Heating:Water',
  nodeType: 'coil-heating-water',
  inputs: [
    {
      id: 'air-inlet',
      position: Position.Left,
      className: 'air-handle',
      style: { top: '30%' }
    },
    {
      id: 'water-inlet',
      position: Position.Bottom,
      className: 'hot-water-handle',
      style: { left: '30%' }
    }
  ],
  outputs: [
    {
      id: 'air-outlet',
      position: Position.Right,
      className: 'air-handle',
      style: { top: '30%' }
    },
    {
      id: 'water-outlet',
      position: Position.Bottom,
      className: 'hot-water-handle',
      style: { left: '70%' }
    }
  ],
  displayProperties: [
    {
      name: 'designWaterFlowRate',
      label: 'Water Flow',
      unit: 'L/s',
      format: (value) => value
    },
    {
      name: 'designAirFlowRate',
      label: 'Air Flow',
      unit: 'm³/s',
      format: (value) => value
    },
    {
      name: 'designInletWaterTemperature',
      label: 'Water Temp',
      unit: '°C',
      format: (value) => value
    }
  ],
  icon: (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x="4" y="6" width="16" height="12" rx="2" stroke="#ef5350" strokeWidth="2"/>
      <path d="M4 10L20 10" stroke="#ef5350" strokeWidth="2"/>
      <path d="M4 14L20 14" stroke="#ef5350" strokeWidth="2"/>
      <path d="M8 6L8 18" stroke="#ef5350" strokeWidth="2"/>
      <path d="M12 6L12 18" stroke="#ef5350" strokeWidth="2"/>
      <path d="M16 6L16 18" stroke="#ef5350" strokeWidth="2"/>
    </svg>
  )
};

export default {
  coilCoolingWaterConfig,
  coilHeatingWaterConfig
};
